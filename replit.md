# Overview

This is a full-stack web application built with React and Express that implements "BratForge" - a comprehensive brat text generator that allows users to create stylized text graphics with customizable colors, fonts, and effects. The application features a modern UI built with shadcn/ui components and Tailwind CSS, providing users with real-time preview capabilities, comprehensive tutorial guides, and image download functionality. The application is fully SEO-optimized around the core keyword "brat text generator" with complete multi-language support (English/Japanese) and dynamic SEO meta tags.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
- **Framework**: React 18 with TypeScript for type safety and modern development patterns
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query (React Query) for server state management and caching
- **UI Components**: shadcn/ui component library built on Radix UI primitives for accessible, customizable components
- **Styling**: Tailwind CSS with custom CSS variables for theming and responsive design
- **Canvas Rendering**: HTML5 Canvas API with custom React hooks for real-time text rendering and image generation

## Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules for modern JavaScript features
- **Build System**: Vite for development and esbuild for production builds
- **Development Server**: Custom Vite integration with Express for hot module replacement
- **Storage Interface**: Abstracted storage layer with in-memory implementation (ready for database integration)

## Data Storage Solutions
- **ORM**: Drizzle ORM configured for PostgreSQL with type-safe database operations
- **Schema**: Centralized schema definitions in `/shared` directory for type consistency
- **Migrations**: Drizzle Kit for database schema management and migrations
- **Current Implementation**: In-memory storage with interface pattern for easy database swapping

## Authentication and Authorization
- **User Schema**: Defined user table structure with username/password fields
- **Session Management**: Connect-pg-simple configured for PostgreSQL session storage
- **Security**: Prepared for secure authentication implementation with proper session handling

## External Dependencies
- **Database**: Neon Database serverless PostgreSQL for scalable cloud database
- **UI Framework**: Radix UI primitives for accessible component foundations
- **Form Handling**: React Hook Form with Zod resolvers for type-safe form validation
- **Date Utilities**: date-fns for date manipulation and formatting
- **Development Tools**: Replit-specific plugins for development environment integration
- **Canvas Libraries**: Embla Carousel for enhanced UI interactions

The architecture follows a monorepo pattern with clear separation between client, server, and shared code, enabling type safety across the full stack while maintaining flexibility for future enhancements.

## Recent Major Updates (August 12, 2025)

### SEO Optimization & Brand Evolution
- **Brand Relaunch**: Successfully rebranded from "brat photo" to "BratForge" - a unique, memorable brand name that emphasizes creative "forging" of text graphics
- **Comprehensive SEO Implementation**: Full optimization around core keyword "brat text generator" including:
  - Dynamic SEO meta tags with `useSEO` and `usePageSEO` hooks
  - Language-specific SEO titles and descriptions
  - Complete Open Graph and Twitter card integration
  - Structured keywords targeting "brat text generator", "free text generator", "social media graphics"
- **Multi-language SEO**: Both English and Japanese versions have optimized SEO content
  - English: "BratForge - Free Brat Text Generator | Create Bold Text Graphics Online"
  - Japanese: "BratForge - 無料ブラットテキストジェネレーター | オンラインで大胆なテキストグラフィックを作成"
- **Content Optimization**: All website copy updated to naturally incorporate "brat text generator" and related keywords while maintaining user-friendly language
- **Technical SEO**: Canonical URLs, proper meta robots tags, and dynamic HTML lang attributes based on selected language

### Multi-Language Expansion (August 12, 2025)
- **Six-Language Support**: Expanded from 3 to 6 supported languages:
  - 🇺🇸 English (en) - Primary language with full feature support
  - 🇯🇵 Japanese (ja) - Complete localization with optimized UI components  
  - 🇷🇺 Russian (ru) - Full translation including TEXT TRANSFORM controls
  - 🇪🇸 Spanish (es) - New comprehensive translation with native content
  - 🇰🇷 Korean (ko) - Complete Korean localization with proper typography
  - 🇩🇪 German (de) - Professional German translation with technical accuracy
- **Translation Infrastructure**: 
  - URL-based language routing (/en, /ja, /ru, /es, /ko, /de)
  - Fallback system ensuring no broken UI elements
  - JSON-based translation files for scalability
  - Dynamic language switching with proper URL updates
- **Component Internationalization**: All UI components fully internationalized including complex forms like TEXT TRANSFORM controls with proper native language labels
- **SEO Multi-Language**: Each language version optimized for local search with native keyword targeting