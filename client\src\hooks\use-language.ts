import { useEffect } from 'react';
import { Language, DEFAULT_LANGUAGE, getTranslations } from '@/lib/i18n';
import { useLocation } from 'wouter';

export const useLanguage = () => {
  const [location, setLocation] = useLocation();
  
  // Extract language from URL path
  const getLanguageFromPath = (path: string): Language => {
    if (path.startsWith('/ja')) return 'ja';
    if (path.startsWith('/ru')) return 'ru';
    if (path.startsWith('/es')) return 'es';
    if (path.startsWith('/ko')) return 'ko';
    if (path.startsWith('/de')) return 'de';
    return DEFAULT_LANGUAGE;
  };

  // Get current language from URL
  const language = getLanguageFromPath(location);

  const setLanguage = (newLanguage: Language) => {
    // Update URL based on language
    if (newLanguage === 'ja') {
      const currentPath = location.replace(/^\/(ja|ru|es|ko|de)/, '') || '/';
      setLocation(`/ja${currentPath}`);
    } else if (newLanguage === 'ru') {
      const currentPath = location.replace(/^\/(ja|ru|es|ko|de)/, '') || '/';
      setLocation(`/ru${currentPath}`);
    } else if (newLanguage === 'es') {
      const currentPath = location.replace(/^\/(ja|ru|es|ko|de)/, '') || '/';
      setLocation(`/es${currentPath}`);
    } else if (newLanguage === 'ko') {
      const currentPath = location.replace(/^\/(ja|ru|es|ko|de)/, '') || '/';
      setLocation(`/ko${currentPath}`);
    } else if (newLanguage === 'de') {
      const currentPath = location.replace(/^\/(ja|ru|es|ko|de)/, '') || '/';
      setLocation(`/de${currentPath}`);
    } else {
      // For English, remove language prefix
      const currentPath = location.replace(/^\/(ja|ru|es|ko|de)/, '') || '/';
      setLocation(currentPath);
    }
  };

  const t = getTranslations(language);

  useEffect(() => {
    // Update document language attribute
    document.documentElement.lang = language;
  }, [language]);

  return {
    language,
    setLanguage,
    t
  };
};