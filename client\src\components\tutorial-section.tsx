import { useLanguage } from "@/hooks/use-language";

export function TutorialSection() {
  const { t } = useLanguage();
  
  const tutorialSteps = [
    {
      title: t.tutorial.detailedSteps.step1.title,
      description: t.tutorial.detailedSteps.step1.description,
      icon: "✍️",
      details: t.tutorial.detailsList.step1
    },
    {
      title: t.tutorial.detailedSteps.step2.title,
      description: t.tutorial.detailedSteps.step2.description,
      icon: "🎨",
      details: t.tutorial.detailsList.step2
    },
    {
      title: t.tutorial.detailedSteps.step3.title,
      description: t.tutorial.detailedSteps.step3.description,
      icon: "📏",
      details: t.tutorial.detailsList.step3
    },
    {
      title: t.tutorial.detailedSteps.step4.title,
      description: t.tutorial.detailedSteps.step4.description,
      icon: "📐",
      details: t.tutorial.detailsList.step4
    },
    {
      title: t.tutorial.detailedSteps.step5.title,
      description: t.tutorial.detailedSteps.step5.description,
      icon: "✨",
      details: t.tutorial.detailsList.step5
    },
    {
      title: t.tutorial.detailedSteps.step6.title,
      description: t.tutorial.detailedSteps.step6.description,
      icon: "🖊️",
      details: t.tutorial.detailsList.step6
    },
    {
      title: t.tutorial.detailedSteps.step7.title,
      description: t.tutorial.detailedSteps.step7.description,
      icon: "📥",
      details: t.tutorial.detailsList.step7
    }
  ];

  const tips = t.tutorial.tipsList;

  return (
    <section id="tutorial" className="py-16 px-4 bg-gradient-to-b from-brat-dark to-brat-gray">
      <div className="container mx-auto max-w-6xl">
        <h2 className="text-3xl md:text-5xl font-black text-center text-brat-cyan glow-text mb-4">
          {t.tutorial.howToUse.title.toUpperCase()}
        </h2>
        <p className="text-center text-gray-300 text-lg mb-12 max-w-2xl mx-auto">
          {t.tutorial.descriptionText}
        </p>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {tutorialSteps.map((step, index) => (
            <div 
              key={index}
              className="bg-brat-gray rounded-xl p-6 border border-gray-700 hover:border-brat-lime transition-all duration-300 hover:transform hover:scale-105"
              data-testid={`tutorial-step-${index + 1}`}
            >
              <div className="flex items-center mb-4">
                <span className="text-3xl mr-3">{step.icon}</span>
                <h3 className="text-brat-lime font-bold text-lg">{step.title}</h3>
              </div>
              <p className="text-gray-300 mb-4">{step.description}</p>
              <ul className="space-y-2">
                {step.details.map((detail, detailIndex) => (
                  <li key={detailIndex} className="text-gray-400 text-sm flex items-start">
                    <span className="text-brat-pink mr-2">•</span>
                    {detail}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Tips Section */}
        <div className="bg-brat-gray rounded-xl p-6 border border-brat-lime">
          <h3 className="text-2xl font-bold text-brat-pink mb-6 text-center">
            {t.tutorial.proTipsTitle}
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            {tips.map((tip, index) => (
              <div 
                key={index}
                className="flex items-center p-3 bg-black rounded-lg border border-gray-700"
                data-testid={`tip-${index + 1}`}
              >
                <span className="text-gray-300">{tip}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Visual Example */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-brat-violet mb-6">
            {t.tutorial.styleExamples.title}
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-black rounded-xl p-4 border border-gray-700">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 h-32 rounded-lg flex items-center justify-center mb-3">
                <span className="text-white font-black text-xl">BRAT ENERGY</span>
              </div>
              <p className="text-gray-400 text-sm">{t.tutorial.styleExamples.gradientBold}</p>
            </div>
            <div className="bg-black rounded-xl p-4 border border-gray-700">
              <div className="bg-lime-400 h-32 rounded-lg flex items-center justify-center mb-3">
                <span className="text-black font-black text-xl" style={{ filter: 'blur(1px)' }}>
                  DREAMY
                </span>
              </div>
              <p className="text-gray-400 text-sm">{t.tutorial.styleExamples.neonBlur}</p>
            </div>
            <div className="bg-black rounded-xl p-4 border border-gray-700">
              <div className="bg-black h-32 rounded-lg flex items-center justify-center mb-3 border border-cyan-400">
                <span className="text-cyan-400 font-black text-xl glow-text">
                  NEON VIBE
                </span>
              </div>
              <p className="text-gray-400 text-sm">{t.tutorial.styleExamples.glowingText}</p>
            </div>
            <div className="bg-black rounded-xl p-4 border border-gray-700">
              <div className="bg-brat-lime h-32 rounded-lg flex items-center justify-center mb-3 relative overflow-hidden">
                <span className="text-black font-black text-xl">CHAOTIC</span>
                <svg className="absolute inset-0 w-full h-full">
                  <path d="M10,20 Q50,10 90,25 T130,30" stroke="#000" strokeWidth="2" fill="none"/>
                  <path d="M15,40 Q45,30 85,45 T125,50" stroke="#000" strokeWidth="2" fill="none"/>
                  <path d="M20,60 Q60,50 100,65 T140,70" stroke="#000" strokeWidth="2" fill="none"/>
                  <path d="M5,80 Q40,70 80,85 T120,90" stroke="#000" strokeWidth="2" fill="none"/>
                </svg>
              </div>
              <p className="text-gray-400 text-sm">{t.tutorial.styleExamples.scribbleEffect}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}