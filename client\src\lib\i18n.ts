// Internationalization configuration and translations
import esTranslations from '../../../es.json';
import koTranslations from '../../../ko.json';
import deTranslations from '../../../de.json';

export type Language = 'en' | 'ja' | 'ru' | 'es' | 'ko' | 'de';

export interface Translations {
  nav: {
    home: string;
    generator: string;
    tutorial: string;
    about: string;
    menu: string;
  };
  header: {
    title: string;
    subtitle: string;
    features: {
      realtime: string;
      colors: string;
      effects: string;
      download: string;
      mobile: string;
    };
  };
  intro: {
    title: string;
    description: string;
  };
  styles: {
    classicBrat: {
      title: string;
      description: string;
      tags: {
        limeGreen: string;
        boldText: string;
        cleanDesign: string;
      };
    };
    scribbleEffect: {
      title: string;
      description: string;
      tags: {
        chaoticLines: string;
        edgyVibes: string;
        customizable: string;
      };
    };
  };
  whyChoose: {
    title: string;
    instantResults: {
      title: string;
      description: string;
    };
    fullCustomization: {
      title: string;
      description: string;
    };
    easyDownload: {
      title: string;
      description: string;
    };
  };
  about: {
    title: string;
    ourMission: {
      title: string;
      description: string;
    };
    whyWeBuilt: {
      title: string;
      description: string;
    };
    completelyFree: {
      title: string;
      description: string;
    };
    builtForEveryone: {
      title: string;
      description: string;
    };
  };
  quickLinks: {
    title: string;
    textGenerator: string;
    tutorial: string;
    aboutUs: string;
  };
  brand: {
    description: string;
  };
  faq: {
    title: string;
    items: Array<{
      question: string;
      answer: string;
    }>;
  };
  controls: {
    textInput: {
      label: string;
      placeholder: string;
    };
    quickStyles: string;
    presets: {
      bratClassic: string;
      hotPink: string;
      neonGlow: string;
      royalPurple: string;
    };
    colors: {
      title: string;
      background: string;
      textColor: string;
      scribbleColor: string;
    };
    textSettings: {
      title: string;
      fontSize: string;
      alignment: string;
      alignments: {
        left: string;
        center: string;
        right: string;
        justify: string;
      };
    };
    effects: {
      title: string;
      blur: string;
      scribble: string;
      intensity: string;
      light: string;
      heavy: string;
      off: string;
    };
    transforms: {
      title: string;
      flipMirror: string;
      flipHorizontal: string;
      flipVertical: string;
      rotation: string;
      rotationLabel: string;
      quickTransform: string;
      presets: {
        mirror: string;
        flip: string;
        upsideDown: string;
        reset: string;
      };
    };
    spacing: {
      title: string;
      letterSpacing: string;
      wordSpacing: string;
      tight: string;
      wide: string;
      close: string;
      far: string;
      presets: {
        title: string;
        wideOpen: string;
        extraWide: string;
        tight: string;
        normal: string;
      };
    };
    download: string;
  };
  canvas: {
    preview: string;
  };
  download: {
    button: string;
  };
  tutorial: {
    title: string;
    description: string;
    detailedSteps: {
      step1: {
        title: string;
        description: string;
      };
      step2: {
        title: string;
        description: string;
      };
      step3: {
        title: string;
        description: string;
      };
      step4: {
        title: string;
        description: string;
      };
      step5: {
        title: string;
        description: string;
      };
      step6: {
        title: string;
        description: string;
      };
      step7: {
        title: string;
        description: string;
      };
    };
    steps: {
      step1: {
        title: string;
        description: string;
      };
      step2: {
        title: string;
        description: string;
      };
      step3: {
        title: string;
        description: string;
      };
      step4: {
        title: string;
        description: string;
      };
    };
    tips: {
      title: string;
      tip1: string;
      tip2: string;
      tip3: string;
      tip4: string;
    };
    howToUse: {
      title: string;
    };
    detailsList: {
      step1: string[];
      step2: string[];
      step3: string[];
      step4: string[];
      step5: string[];
      step6: string[];
      step7: string[];
    };
    tipsList: string[];
    descriptionText: string;
    proTipsTitle: string;
    styleExamples: {
      title: string;
      gradientBold: string;
      neonBlur: string;
      glowingText: string;
      scribbleEffect: string;
    };
  };
  footer: {
    quickLinks: {
      title: string;
    };
    features: {
      title: string;
      realtime: string;
      customColors: string;
      scribbleEffects: string;
      pngDownload: string;
      mobileFriendly: string;
    };
    info: {
      title: string;
      free: string;
      noRegistration: string;
      noWatermarks: string;
      privacy: string;
      madeWithLove: string;
    };
    copyright: string;
    madeWith: string;
    freeForever: string;
    links: {
      privacy: string;
      terms: string;
      contact: string;
    };
  };
  errors: {
    notFound: {
      title: string;
      description: string;
    };
  };
  defaults: {
    sampleText: string;
  };
}

// English translations
const enTranslations: Translations = {
  nav: {
    home: "Home",
    generator: "Generator",
    tutorial: "Tutorial",
    about: "About",
    menu: "Menu"
  },
  header: {
    title: "BratForge",
    subtitle: "Create sassy and customized text images with bold colors and effects! Perfect for social media, designs, and expressing your inner brat energy ✨",
    features: {
      realtime: "⚡ Real-time Preview",
      colors: "🎨 Custom Colors",
      effects: "🖊️ Scribble Effects",
      download: "💾 PNG Download",
      mobile: "📱 Mobile Friendly"
    }
  },
  intro: {
    title: "What is BratForge?",
    description: "The ultimate tool for creating bold, stylish text graphics inspired by the iconic brat aesthetic. Transform your words into eye-catching visuals with custom colors, trendy effects, and that perfect rebellious vibe."
  },
  styles: {
    classicBrat: {
      title: "🌟 Classic Brat Style",
      description: "The signature brat look features bold black text on vibrant lime green backgrounds. Clean, impactful, and instantly recognizable - perfect for making a statement.",
      tags: {
        limeGreen: "Lime Green",
        boldText: "Bold Text",
        cleanDesign: "Clean Design"
      }
    },
    scribbleEffect: {
      title: "🖊️ Scribble Effect",
      description: "Add chaotic energy with our signature scribble effect. Random, rebellious lines that give your text that authentic messy, hand-drawn vibe - because sometimes perfect is boring.",
      tags: {
        chaoticLines: "Chaotic Lines",
        edgyVibes: "Edgy Vibes",
        customizable: "Customizable"
      }
    }
  },
  whyChoose: {
    title: "Why Choose BRAT TEXT GENERATOR?",
    instantResults: {
      title: "Instant Results",
      description: "Real-time preview as you type and customize"
    },
    fullCustomization: {
      title: "Full Customization",
      description: "Colors, fonts, effects, and alignment controls"
    },
    easyDownload: {
      title: "Easy Download",
      description: "High-quality PNG export ready for sharing"
    }
  },
  about: {
    title: "About BRAT TEXT GENERATOR",
    ourMission: {
      title: "Our Mission",
      description: "We believe everyone deserves to express their inner brat with style. Our generator makes it easy to create bold, eye-catching text graphics that capture that perfect mix of confidence and rebellion."
    },
    whyWeBuilt: {
      title: "Why We Built This",
      description: "Tired of boring, generic text tools? So were we. We created this generator specifically for the brat aesthetic - vibrant colors, bold fonts, and that unmistakable edge that makes your content stand out."
    },
    completelyFree: {
      title: "Completely Free",
      description: "No hidden fees, no subscriptions, no watermarks. Create as many text images as you want, download them in high quality, and use them anywhere. Because creativity shouldn't have a price tag."
    },
    builtForEveryone: {
      title: "Built for Everyone",
      description: "Whether you're creating social media content, designing graphics, or just having fun, our tool works on any device. Desktop, tablet, mobile - your brat energy doesn't take breaks."
    }
  },
  quickLinks: {
    title: "Quick Links",
    textGenerator: "Text Generator",
    tutorial: "Tutorial",
    aboutUs: "About Us"
  },
  brand: {
    description: "Create stunning text images with the iconic brat aesthetic. Bold, stylish, and totally free."
  },
  faq: {
    title: "FREQUENTLY ASKED QUESTIONS",
    items: [
      {
        question: "How do I use the Brat Text Generator?",
        answer: "Type your text, customize the colors, font size, and effects, and see the preview update in real-time. Click \"Download Image\" to save your design."
      },
      {
        question: "Can I change the background and text color?",
        answer: "Yes! Use the color picker or enter a hex code for both background and text colors."
      },
      {
        question: "What is the blur effect for?",
        answer: "The blur effect adds a soft, dreamy look to your text."
      },
      {
        question: "How do I align the text?",
        answer: "Click the \"Text Alignment\" button to switch between left, right, center, and justify alignments."
      },
      {
        question: "Why is my text getting cut off?",
        answer: "If the text is too long, reduce the font size to fit it within the canvas properly."
      },
      {
        question: "Can I use special characters or emojis?",
        answer: "Yes! You can include special characters, emojis, and symbols in your text."
      },
      {
        question: "What format is the downloaded image?",
        answer: "The image is downloaded as a high-quality PNG file."
      },
      {
        question: "Can I use this tool on my phone?",
        answer: "Yes! The Brat Text Generator is mobile-friendly and works on all devices."
      },
      {
        question: "Is this tool free to use?",
        answer: "Yes! It is completely free with no hidden charges."
      },
      {
        question: "Who can use this tool?",
        answer: "Anyone! Whether you're a social media user, a designer, or just having fun, this tool is for you!"
      }
    ]
  },
  controls: {
    textInput: {
      label: "Your Text",
      placeholder: "Enter your brat text here..."
    },
    quickStyles: "Quick Styles",
    presets: {
      bratClassic: "🖊️ Brat Classic",
      hotPink: "💖 Hot Pink",
      neonGlow: "⚡ Neon Glow",
      royalPurple: "👑 Royal Purple"
    },
    colors: {
      title: "Colors",
      background: "Background",
      textColor: "Text Color",
      scribbleColor: "Scribble Color"
    },
    textSettings: {
      title: "Text Settings",
      fontSize: "Font Size",
      alignment: "Alignment",
      alignments: {
        left: "Left",
        center: "Center",
        right: "Right",
        justify: "Justify"
      }
    },
    effects: {
      title: "Effects",
      blur: "Blur Effect",
      scribble: "Scribble Effect",
      intensity: "Scribble Intensity",
      light: "Light",
      heavy: "Heavy",
      off: "OFF"
    },
    transforms: {
      title: "TEXT TRANSFORM",
      flipMirror: "Flip & Mirror",
      flipHorizontal: "Flip Horizontal",
      flipVertical: "Flip Vertical",
      rotation: "Rotation",
      rotationLabel: "Rotation: 0°",
      quickTransform: "Quick Transform",
      presets: {
        mirror: "🪞 Mirror",
        flip: "🔄 Flip",
        upsideDown: "🙃 Upside Down",
        reset: "🔄 Reset"
      }
    },
    spacing: {
      title: "Text Spacing",
      letterSpacing: "Letter Spacing",
      wordSpacing: "Word Spacing",
      tight: "Tight",
      wide: "Wide",
      close: "Close",
      far: "Far",
      presets: {
        title: "Presets",
        wideOpen: "Wide Open",
        extraWide: "Extra Wide",
        tight: "Tight",
        normal: "Normal"
      }
    },
    download: "Download PNG"
  },
  canvas: {
    preview: "Canvas Preview"
  },
  download: {
    button: "Download PNG"
  },
  tutorial: {
    title: "How to Create Your Perfect Brat Photo",
    description: "Follow these simple steps to create stunning text graphics that capture your rebellious spirit",
    detailedSteps: {
      step1: {
        title: "1. Enter Your Text",
        description: "Type your desired text into the input box. Supports multiple languages, special characters, and emojis."
      },
      step2: {
        title: "2. Choose Color Scheme",
        description: "Use color pickers to adjust background and text colors, or enter hex codes directly."
      },
      step3: {
        title: "3. Adjust Font Size",
        description: "Use the slider to set font size from 12px to 120px, perfect for any use case."
      },
      step4: {
        title: "4. Set Text Alignment",
        description: "Choose your text alignment: left, center, right, or justify."
      },
      step5: {
        title: "5. Add Blur Effects",
        description: "Use the blur slider to add dreamy, soft effects that create unique visual styles."
      },
      step6: {
        title: "6. Apply Scribble Effects",
        description: "Toggle scribble effect to add chaotic, hand-drawn lines over your text for a rebellious look."
      },
      step7: {
        title: "7. Download Your Creation",
        description: "Click download to save as high-quality PNG, ready for social media sharing."
      }
    },
    steps: {
      step1: {
        title: "Enter Your Text",
        description: "Type the words you want to transform. Short, punchy phrases work best for maximum impact!"
      },
      step2: {
        title: "Choose Colors",
        description: "Pick bold background and text colors. High contrast combinations create that signature brat aesthetic."
      },
      step3: {
        title: "Add Effects",
        description: "Apply blur and scribble effects to give your text that edgy, authentic look."
      },
      step4: {
        title: "Download & Share",
        description: "Save your creation as PNG and share your brat energy with the world!"
      }
    },
    tips: {
      title: "Pro Tips",
      tip1: "Use high contrast colors for maximum impact",
      tip2: "Short phrases work better than long sentences",
      tip3: "Experiment with different effects for unique looks",
      tip4: "Scribble effects add that authentic brat touch"
    },
    howToUse: {
      title: "How to Use"
    },
    detailsList: {
      step1: [
        "Multi-line text support",
        "Use emojis and symbols 🎉",
        "Special characters included",
        "Real-time preview updates"
      ],
      step2: [
        "Click color squares to open picker",
        "Manual hex input (#FFFFFF format)",
        "Independent background and text colors",
        "Transparency and gradient support"
      ],
      step3: [
        "Smaller fonts for longer text",
        "Larger fonts for titles and slogans",
        "Live font size display",
        "Auto-wrap prevents text cutoff"
      ],
      step4: [
        "L = Left align (for paragraphs)",
        "C = Center (for titles)",
        "R = Right align (for signatures)",
        "J = Justify (for formal documents)"
      ],
      step5: [
        "0 = No blur effect",
        "1-5 = Subtle blur, soft effect",
        "6-10 = Strong blur, artistic effect",
        "Perfect for atmospheric text"
      ],
      step6: [
        "Toggle switch to enable/disable",
        "Adjust intensity from light to heavy",
        "Choose custom scribble color",
        "Perfect for that 'brat' aesthetic"
      ],
      step7: [
        "High-quality PNG format",
        "Maintains original clarity",
        "Perfect for all social platforms",
        "Auto-named 'brat-text.png'"
      ]
    },
    tipsList: [
      "💡 If text gets cut off, reduce the font size",
      "🎨 Try high-contrast color combinations for best results",
      "📱 Fully optimized for mobile and tablet devices",
      "⚡ All adjustments show real-time preview",
      "🆓 Completely free to use, no registration required"
    ],
    descriptionText: "Follow these simple steps to create your personalized text images",
    proTipsTitle: "💫 PRO TIPS",
    styleExamples: {
      title: "🖼️ STYLE EXAMPLES",
      gradientBold: "Gradient Background + Bold Text",
      neonBlur: "Neon Green + Soft Blur",
      glowingText: "Dark Background + Glowing Text",
      scribbleEffect: "Lime Green + Scribble Effect"
    }
  },
  footer: {
    quickLinks: {
      title: "Quick Links"
    },
    features: {
      title: "Features",
      realtime: "⚡ Real-time Preview",
      customColors: "🎨 Custom Colors",
      scribbleEffects: "🖊️ Scribble Effects",
      pngDownload: "💾 PNG Download",
      mobileFriendly: "📱 Mobile Friendly"
    },
    info: {
      title: "Info",
      free: "🆓 Totally Free",
      noRegistration: "🚀 No Registration",
      noWatermarks: "⭐ No Watermarks",
      privacy: "🔒 Privacy First",
      madeWithLove: "💖 Made with Love"
    },
    copyright: "All rights reserved",
    madeWith: "Made with",
    freeForever: "Free forever",
    links: {
      privacy: "Privacy Policy",
      terms: "Terms of Service",
      contact: "Contact"
    }
  },
  errors: {
    notFound: {
      title: "404 Page Not Found",
      description: "Did you forget to add the page to the router?"
    }
  },
  defaults: {
    sampleText: "BRAT ENERGY"
  }
};

// Japanese translations
const jaTranslations: Translations = {
  nav: {
    home: "ホーム",
    generator: "ジェネレーター",
    tutorial: "チュートリアル",
    about: "について",
    menu: "メニュー"
  },
  header: {
    title: "BratForge",
    subtitle: "大胆でカスタマイズされたテキスト画像を作成しよう！SNS、デザイン、そして内なるブラットエナジーを表現するのにぴったり ✨",
    features: {
      realtime: "⚡ リアルタイムプレビュー",
      colors: "🎨 カスタムカラー",
      effects: "🖊️ スクリブル効果",
      download: "💾 PNG ダウンロード",
      mobile: "📱 モバイル対応"
    }
  },
  intro: {
    title: "BratForge とは？",
    description: "象徴的なブラット美学にインスパイアされた、大胆でスタイリッシュなテキストグラフィックを作成するための究極のツールです。カスタムカラー、トレンディな効果、そして完璧な反抗的な雰囲気で、あなたの言葉を目を引くビジュアルに変換します。"
  },
  styles: {
    classicBrat: {
      title: "🌟 クラシックブラットスタイル",
      description: "シグネチャーブラットルックは、鮮やかなライムグリーンの背景に太字の黒いテキストを特徴としています。クリーンで印象的で、瞬時に認識できる - ステートメントを作るのに最適です。",
      tags: {
        limeGreen: "ライムグリーン",
        boldText: "太字テキスト",
        cleanDesign: "クリーンデザイン"
      }
    },
    scribbleEffect: {
      title: "🖊️ スクリブル効果",
      description: "シグネチャースクリブル効果でカオスなエネルギーを加えましょう。ランダムで反抗的な線があなたのテキストに本物の散らかった手描きの雰囲気を与えます - 時には完璧は退屈だから。",
      tags: {
        chaoticLines: "カオスライン",
        edgyVibes: "エッジィーバイブス",
        customizable: "カスタマイズ可能"
      }
    }
  },
  whyChoose: {
    title: "なぜBRAT TEXT GENERATORを選ぶのか？",
    instantResults: {
      title: "インスタント結果",
      description: "タイプしてカスタマイズするときのリアルタイムプレビュー"
    },
    fullCustomization: {
      title: "完全カスタマイゼーション",
      description: "カラー、フォント、効果、および配置コントロール"
    },
    easyDownload: {
      title: "簡単ダウンロード",
      description: "共有準備の整った高品質PNG出力"
    }
  },
  about: {
    title: "BRAT TEXT GENERATORについて",
    ourMission: {
      title: "私たちのミッション",
      description: "私たちは、誰もが内なるブラットをスタイリッシュに表現する権利があると信じています。私たちのジェネレーターは、自信と反抗の完璧なミックスを捉えた、大胆で目を引くテキストグラフィックを簡単に作成できるようにします。"
    },
    whyWeBuilt: {
      title: "なぜこれを作ったのか",
      description: "退屈で一般的なテキストツールにうんざりしていませんか？私たちもそうでした。私たちは特にブラット美学のためにこのジェネレーターを作成しました - 鮮やかな色、太字のフォント、そしてあなたのコンテンツを際立たせるその間違いないエッジ。"
    },
    completelyFree: {
      title: "完全無料",
      description: "隠れた料金、サブスクリプション、ウォーターマークはありません。好きなだけテキスト画像を作成し、高品質でダウンロードし、どこでも使用してください。創造性に値札を付けるべきではないからです。"
    },
    builtForEveryone: {
      title: "すべての人のために作られた",
      description: "ソーシャルメディアコンテンツを作成している場合でも、グラフィックをデザインしている場合でも、または単に楽しんでいる場合でも、私たちのツールはあらゆるデバイスで動作します。デスクトップ、タブレット、モバイル - あなたのブラットエナジーは休憩を取りません。"
    }
  },
  quickLinks: {
    title: "クイックリンク",
    textGenerator: "テキストジェネレーター",
    tutorial: "チュートリアル",
    aboutUs: "私たちについて"
  },
  brand: {
    description: "象徴的なブラット美学で美しいテキスト画像を作成します。大胆でスタイリッシュで、完全に無料です。"
  },
  faq: {
    title: "よくある質問",
    items: [
      {
        question: "Bratテキストジェネレーターの使い方は？",
        answer: "テキストを入力し、色、フォントサイズ、効果をカスタマイズすると、リアルタイムでプレビューが更新されます。「画像をダウンロード」をクリックしてデザインを保存してください。"
      },
      {
        question: "背景色とテキスト色を変更できますか？",
        answer: "はい！カラーピッカーを使用するか、背景色とテキスト色の両方に16進コードを入力してください。"
      },
      {
        question: "ブラー効果は何のためですか？",
        answer: "ブラー効果はテキストにソフトで夢のような外観を追加します。"
      },
      {
        question: "テキストを整列する方法は？",
        answer: "「テキスト配置」ボタンをクリックして、左揃え、右揃え、中央揃え、両端揃えを切り替えます。"
      },
      {
        question: "テキストが切れてしまうのはなぜですか？",
        answer: "テキストが長すぎる場合は、フォントサイズを小さくしてキャンバス内に適切に収まるようにしてください。"
      },
      {
        question: "特殊文字や絵文字を使用できますか？",
        answer: "はい！テキストに特殊文字、絵文字、記号を含めることができます。"
      },
      {
        question: "ダウンロードされる画像の形式は？",
        answer: "画像は高品質のPNGファイルとしてダウンロードされます。"
      },
      {
        question: "このツールをスマートフォンで使用できますか？",
        answer: "はい！Bratテキストジェネレーターはモバイル対応で、すべてのデバイスで動作します。"
      },
      {
        question: "このツールは無料で使用できますか？",
        answer: "はい！隠れた料金なしで完全に無料です。"
      },
      {
        question: "誰がこのツールを使用できますか？",
        answer: "誰でも！ソーシャルメディアユーザー、デザイナー、または単に楽しみたい方、このツールはあなたのためのものです！"
      }
    ]
  },
  controls: {
    textInput: {
      label: "テキスト入力",
      placeholder: "ブラットなテキストを入力してください..."
    },
    quickStyles: "クイックスタイル",
    presets: {
      bratClassic: "🖊️ ブラットクラシック",
      hotPink: "💖 ホットピンク",
      neonGlow: "⚡ ネオングロー",
      royalPurple: "👑 ロイヤルパープル"
    },
    colors: {
      title: "カラー",
      background: "背景色",
      textColor: "テキスト色",
      scribbleColor: "スクリブル色"
    },
    textSettings: {
      title: "テキスト設定",
      fontSize: "フォントサイズ",
      alignment: "配置",
      alignments: {
        left: "左揃え",
        center: "中央揃え",
        right: "右揃え",
        justify: "両端揃え"
      }
    },
    effects: {
      title: "エフェクト",
      blur: "ぼかし効果",
      scribble: "スクリブル効果",
      intensity: "スクリブル強度",
      light: "軽い",
      heavy: "強い",
      off: "オフ"
    },
    transforms: {
      title: "テキスト変形",
      flipMirror: "反転とミラー",
      flipHorizontal: "水平反転",
      flipVertical: "垂直反転",
      rotation: "回転",
      rotationLabel: "回転: 0°",
      quickTransform: "クイック変換",
      presets: {
        mirror: "🪞 ミラー",
        flip: "🔄 反転",
        upsideDown: "🙃 上下逆さま",
        reset: "🔄 リセット"
      }
    },
    spacing: {
      title: "テキスト間隔",
      letterSpacing: "文字間隔",
      wordSpacing: "単語間隔",
      tight: "狭い",
      wide: "広い",
      close: "近い",
      far: "遠い",
      presets: {
        title: "プリセット",
        wideOpen: "ワイドオープン",
        extraWide: "エクストラワイド",
        tight: "タイト",
        normal: "通常"
      }
    },
    download: "PNG ダウンロード"
  },
  canvas: {
    preview: "キャンバスプレビュー"
  },
  download: {
    button: "PNG ダウンロード"
  },
  tutorial: {
    title: "完璧なブラット写真の作り方",
    description: "反抗的な精神を表現する美しいテキストグラフィックを作成するための簡単なステップ",
    detailedSteps: {
      step1: {
        title: "1. テキストを入力",
        description: "入力ボックスに目的のテキストを入力してください。多言語、特殊文字、絵文字をサポートしています。"
      },
      step2: {
        title: "2. カラースキームを選択",
        description: "カラーピッカーを使用して背景色とテキスト色を調整するか、16進コードを直接入力してください。"
      },
      step3: {
        title: "3. フォントサイズを調整",
        description: "スライダーを使用して12pxから120pxまでフォントサイズを設定でき、あらゆる用途に最適です。"
      },
      step4: {
        title: "4. テキスト配置を設定",
        description: "テキストの配置を選択してください：左、中央、右、または両端揃え。"
      },
      step5: {
        title: "5. ぼかし効果を追加",
        description: "ぼかしスライダーを使用して、夢のような柔らかな効果を追加し、ユニークな視覚スタイルを作成します。"
      },
      step6: {
        title: "6. スクリブル効果を適用",
        description: "スクリブル効果を切り替えて、テキストの上に無秩序で手描きのような線を追加し、反抗的な見た目を演出します。"
      },
      step7: {
        title: "7. 作品をダウンロード",
        description: "ダウンロードをクリックして、高品質なPNGとして保存し、ソーシャルメディアでの共有に備えます。"
      }
    },
    steps: {
      step1: {
        title: "テキストを入力",
        description: "変換したい言葉を入力してください。短くてパンチのあるフレーズが最大の効果を発揮します！"
      },
      step2: {
        title: "カラーを選択",
        description: "大胆な背景色とテキスト色を選んでください。高コントラストの組み合わせがブラット美学の特徴を作り出します。"
      },
      step3: {
        title: "エフェクトを追加",
        description: "ぼかしとスクリブル効果を適用して、テキストにエッジの効いた本格的な見た目を与えましょう。"
      },
      step4: {
        title: "ダウンロード＆シェア",
        description: "作品をPNGで保存して、あなたのブラットエナジーを世界と共有しましょう！"
      }
    },
    tips: {
      title: "プロのコツ",
      tip1: "最大の効果を得るために高コントラストの色を使用",
      tip2: "長い文よりも短いフレーズの方が効果的",
      tip3: "ユニークな見た目のために様々な効果を実験",
      tip4: "スクリブル効果が本格的なブラットタッチを追加"
    },
    howToUse: {
      title: "使い方"
    },
    detailsList: {
      step1: [
        "複数行のテキストサポート",
        "絵文字やシンボルを使用 🎉",
        "特殊文字を含む",
        "リアルタイムプレビュー更新"
      ],
      step2: [
        "カラースクエアをクリックしてピッカーを開く",
        "手動16進コード入力（#FFFFFF形式）",
        "独立した背景色とテキスト色",
        "透明度とグラデーションサポート"
      ],
      step3: [
        "長いテキストには小さなフォント",
        "タイトルやスローガンには大きなフォント",
        "リアルタイムフォントサイズ表示",
        "自動折り返しでテキストカットを防止"
      ],
      step4: [
        "L = 左揃え（段落用）",
        "C = 中央揃え（タイトル用）",
        "R = 右揃え（署名用）",
        "J = 両端揃え（正式文書用）"
      ],
      step5: [
        "0 = ぼかし効果なし",
        "1-5 = 微細なぼかし、柔らかい効果",
        "6-10 = 強いぼかし、芸術的効果",
        "雰囲気のあるテキストに最適"
      ],
      step6: [
        "トグルスイッチで有効/無効",
        "軽いものから重いものまで強度調整",
        "カスタムスクリブル色選択",
        "完璧な'ブラット'美学"
      ],
      step7: [
        "高品質PNG形式",
        "元の明瞭さを維持",
        "全てのソーシャルプラットフォームに最適",
        "'brat-text.png'として自動命名"
      ]
    },
    tipsList: [
      "💡 テキストが切れた場合はフォントサイズを小さくする",
      "🎨 最良の結果のために高コントラストの色の組み合わせを試す",
      "📱 モバイルとタブレットデバイスに完全最適化",
      "⚡ すべての調整がリアルタイムプレビューを表示",
      "🆓 完全無料で使用、登録不要"
    ],
    descriptionText: "個人用テキスト画像を作成するためのこれらの簡単なステップに従ってください",
    proTipsTitle: "💫 プロのコツ",
    styleExamples: {
      title: "🖼️ スタイル例",
      gradientBold: "グラデーション背景 + 太字テキスト",
      neonBlur: "ネオングリーン + ソフトブラー",
      glowingText: "ダーク背景 + 光るテキスト",
      scribbleEffect: "ライムグリーン + スクリブル効果"
    }
  },
  footer: {
    quickLinks: {
      title: "クイックリンク"
    },
    features: {
      title: "機能",
      realtime: "⚡ リアルタイムプレビュー",
      customColors: "🎨 カスタムカラー",
      scribbleEffects: "🖊️ スクリブル効果",
      pngDownload: "💾 PNG ダウンロード",
      mobileFriendly: "📱 モバイル対応"
    },
    info: {
      title: "情報",
      free: "🆓 完全無料",
      noRegistration: "🚀 登録不要",
      noWatermarks: "⭐ ウォーターマークなし",
      privacy: "🔒 プライバシー第一",
      madeWithLove: "💖 愛をこめて制作"
    },
    copyright: "著作権所有",
    madeWith: "製作：",
    freeForever: "永続無料",
    links: {
      privacy: "プライバシーポリシー",
      terms: "利用規約",
      contact: "お問い合わせ"
    }
  },
  errors: {
    notFound: {
      title: "404 ページが見つかりません",
      description: "ページをルーターに追加するのを忘れましたか？"
    }
  },
  defaults: {
    sampleText: "ブラットエナジー"
  }
};

// Russian translations
const ruTranslations: Translations = {
  nav: {
    home: "Главная",
    generator: "Генератор",
    tutorial: "Руководство",
    about: "О нас",
    menu: "Меню"
  },
  header: {
    title: "BratForge",
    subtitle: "Создавайте потрясающую брат-графику онлайн бесплатно! Генерируйте смелые, бунтарские текстовые изображения с пользовательскими цветами, эффектами и фирменной брат-эстетикой. Идеально для социальных сетей, мемов и креативных дизайнов ✨",
    features: {
      realtime: "⚡ Предварительный просмотр",
      colors: "🎨 Пользовательские цвета",
      effects: "🖊️ Эффекты каракулей",
      download: "💾 PNG загрузка",
      mobile: "📱 Мобильная версия"
    }
  },
  intro: {
    title: "Что такое BratForge - Лучший генератор брат-текста?",
    description: "BratForge - ведущий бесплатный онлайн-генератор брат-текста! Создавайте потрясающую текстовую графику с характерной бунтарской брат-эстетикой. Наш продвинутый текстовый генератор превращает ваши слова в смелые, привлекающие внимание визуальные элементы, идеальные для постов в социальных сетях, мемов и креативных дизайнов. Регистрация не требуется!"
  },
  styles: {
    classicBrat: {
      title: "🌟 Классический брат-стиль",
      description: "Фирменный брат-образ с жирным черным текстом на ярких лаймово-зеленых фонах. Чисто, эффектно и мгновенно узнаваемо.",
      tags: {
        limeGreen: "Лаймово-зеленый",
        boldText: "Жирный текст",
        cleanDesign: "Чистый дизайн"
      }
    },
    scribbleEffect: {
      title: "🖊️ Эффект каракулей",
      description: "Добавьте хаотичную энергию с нашим фирменным эффектом каракулей. Случайные, бунтарские линии для аутентичного рукописного вида.",
      tags: {
        chaoticLines: "Хаотичные линии",
        edgyVibes: "Резкие вибрации",
        customizable: "Настраиваемый"
      }
    }
  },
  whyChoose: {
    title: "Почему выбрать BRATFORGE?",
    instantResults: {
      title: "Мгновенные результаты",
      description: "Просмотр в реальном времени при печати и настройке"
    },
    fullCustomization: {
      title: "Полная настройка",
      description: "Цвета, шрифты, эффекты и управление выравниванием"
    },
    easyDownload: {
      title: "Легкая загрузка",
      description: "Высококачественный PNG-экспорт готов для публикации"
    }
  },
  about: {
    title: "О BRATFORGE",
    ourMission: {
      title: "Наша миссия",
      description: "Мы верим, что каждый имеет право выражать своего внутреннего бунтаря стильно."
    },
    whyWeBuilt: {
      title: "Почему мы это создали",
      description: "Мы заметили недостаток хороших, бесплатных инструментов для создания брат-эстетики."
    },
    completelyFree: {
      title: "Полностью бесплатно",
      description: "Никаких скрытых платежей, подписок или водяных знаков."
    },
    builtForEveryone: {
      title: "Создано для всех",
      description: "От новичков до профессиональных дизайнеров - каждый может создать потрясающую графику."
    }
  },
  quickLinks: {
    title: "Быстрые ссылки",
    textGenerator: "Генератор текста",
    tutorial: "Руководство",
    aboutUs: "О нас"
  },
  brand: {
    description: "BratForge - премиальный бесплатный генератор брат-текста для создания потрясающей текстовой графики онлайн."
  },
  faq: {
    title: "Часто задаваемые вопросы",
    items: [
      {
        question: "Это действительно бесплатно?",
        answer: "Да! BratForge полностью бесплатен для использования. Никаких скрытых платежей, подписок или ограничений."
      },
      {
        question: "Нужна ли регистрация?",
        answer: "Нет! Вы можете начать создавать брат-графику сразу же, без регистрации или создания аккаунта."
      },
      {
        question: "Какие форматы поддерживаются для скачивания?",
        answer: "В настоящее время мы поддерживаем высококачественные PNG-загрузки, идеальные для социальных сетей и печати."
      }
    ]
  },
  controls: {
    textInput: {
      label: "Ваш текст",
      placeholder: "Введите текст для генератора брат-текста..."
    },
    quickStyles: "Быстрые стили",
    presets: {
      bratClassic: "🖊️ Классический брат",
      hotPink: "💖 Горячий розовый",
      neonGlow: "⚡ Неоновое свечение",
      royalPurple: "👑 Королевский фиолетовый"
    },
    colors: {
      title: "Цвета",
      background: "Фон",
      textColor: "Цвет текста",
      scribbleColor: "Цвет каракулей"
    },
    textSettings: {
      title: "Настройки текста",
      fontSize: "Размер шрифта",
      alignment: "Выравнивание",
      alignments: {
        left: "Слева",
        center: "По центру",
        right: "Справа",
        justify: "По ширине"
      }
    },
    effects: {
      title: "Эффекты",
      blur: "Эффект размытия",
      scribble: "Эффект каракулей",
      intensity: "Интенсивность каракулей",
      light: "Легкая",
      heavy: "Сильная",
      off: "ВЫКЛ"
    },
    transforms: {
      title: "ТРАНСФОРМАЦИЯ ТЕКСТА",
      flipMirror: "Отражение и Зеркало",
      flipHorizontal: "Отразить по горизонтали",
      flipVertical: "Отразить по вертикали",
      rotation: "Поворот",
      rotationLabel: "Поворот: 0°",
      quickTransform: "Быстрая трансформация",
      presets: {
        mirror: "🪞 Зеркало",
        flip: "🔄 Отражение",
        upsideDown: "🙃 Вверх ногами",
        reset: "🔄 Сброс"
      }
    },
    spacing: {
      title: "Интервалы текста",
      letterSpacing: "Интервал между буквами",
      wordSpacing: "Интервал между словами",
      tight: "Узкий",
      wide: "Широкий",
      close: "Близко",
      far: "Далеко",
      presets: {
        title: "Пресеты",
        wideOpen: "Широко открыто",
        extraWide: "Очень широко",
        tight: "Узко",
        normal: "Обычно"
      }
    },
    download: "Скачать PNG"
  },
  canvas: {
    preview: "Предварительный просмотр холста"
  },
  download: {
    button: "Скачать PNG"
  },
  tutorial: {
    title: "Как использовать BratForge",
    description: "Освойте наш бесплатный генератор брат-текста за считанные минуты!",
    detailedSteps: {
      step1: {
        title: "1. Введите ваш текст",
        description: "Введите желаемый текст в поле ввода."
      },
      step2: {
        title: "2. Выберите цветовую схему",
        description: "Используйте цветовые палитры для настройки цветов."
      },
      step3: {
        title: "3. Настройте размер шрифта",
        description: "Используйте слайдер для установки размера шрифта."
      },
      step4: {
        title: "4. Установите выравнивание текста",
        description: "Выберите выравнивание текста."
      },
      step5: {
        title: "5. Добавьте эффекты размытия",
        description: "Используйте слайдер размытия для добавления эффектов."
      },
      step6: {
        title: "6. Примените эффекты каракулей",
        description: "Включите эффект каракулей для бунтарского вида."
      },
      step7: {
        title: "7. Скачайте ваше творение",
        description: "Нажмите кнопку загрузки для сохранения высококачественного PNG файла."
      }
    },
    steps: {
      step1: {
        title: "Введите ваш текст",
        description: "Введите слова для трансформации."
      },
      step2: {
        title: "Выберите цвета",
        description: "Выберите смелые цвета фона и текста."
      },
      step3: {
        title: "Добавьте эффекты",
        description: "Примените эффекты размытия и каракулей."
      },
      step4: {
        title: "Скачайте и поделитесь",
        description: "Сохраните ваше творение в формате PNG!"
      }
    },
    tips: {
      title: "Профессиональные советы",
      tip1: "Используйте высококонтрастные цвета",
      tip2: "Короткие фразы работают лучше",
      tip3: "Экспериментируйте с эффектами",
      tip4: "Каракули добавляют аутентичность"
    },
    howToUse: {
      title: "Как использовать"
    },
    detailsList: {
      step1: [
        "Поддержка многострочного текста",
        "Используйте эмодзи и символы 🎉",
        "Включает специальные символы",
        "Обновления предпросмотра в реальном времени"
      ],
      step2: [
        "Нажмите цветовые квадраты для открытия палитры",
        "Ручной ввод hex-кодов (#FFFFFF формат)",
        "Независимые цвета фона и текста",
        "Поддержка прозрачности и градиентов"
      ],
      step3: [
        "Меньшие шрифты для длинного текста",
        "Большие шрифты для заголовков и слоганов",
        "Отображение размера шрифта в реальном времени",
        "Автоперенос для предотвращения обрезки текста"
      ],
      step4: [
        "L = Выравнивание слева (для параграфов)",
        "C = Выравнивание по центру (для заголовков)",
        "R = Выравнивание справа (для подписей)",
        "J = Выравнивание по ширине (для официальных документов)"
      ],
      step5: [
        "0 = Без эффекта размытия",
        "1-5 = Тонкое размытие, мягкие эффекты",
        "6-10 = Сильное размытие, художественные эффекты",
        "Идеально для атмосферного текста"
      ],
      step6: [
        "Переключатель для включения/отключения",
        "Регулировка интенсивности от легкой до тяжелой",
        "Выбор пользовательского цвета каракулей",
        "Идеальная 'брат' эстетика"
      ],
      step7: [
        "Высококачественный PNG формат",
        "Сохраняет оригинальную четкость",
        "Оптимизирован для всех социальных платформ",
        "Автоматическое именование как 'brat-text.png'"
      ]
    },
    tipsList: [
      "💡 Уменьшите размер шрифта если текст обрезается",
      "🎨 Попробуйте высококонтрастные цветовые сочетания для лучших результатов",
      "📱 Полностью оптимизировано для мобильных и планшетных устройств",
      "⚡ Все настройки показывают предпросмотр в реальном времени",
      "🆓 Полностью бесплатно для использования, регистрация не требуется"
    ],
    descriptionText: "Следуйте этим простым шагам для создания персонализированных текстовых изображений",
    proTipsTitle: "💫 Профессиональные советы",
    styleExamples: {
      title: "🖼️ Примеры стилей",
      gradientBold: "Градиентный фон + жирный текст",
      neonBlur: "Неоново-зеленый + мягкое размытие",
      glowingText: "Темный фон + светящийся текст",
      scribbleEffect: "Лаймово-зеленый + эффект каракулей"
    }
  },
  footer: {
    quickLinks: {
      title: "Быстрые ссылки"
    },
    features: {
      title: "Функции",
      realtime: "⚡ Предварительный просмотр",
      customColors: "🎨 Пользовательские цвета",
      scribbleEffects: "🖊️ Эффекты каракулей",
      pngDownload: "💾 PNG загрузка",
      mobileFriendly: "📱 Мобильная версия"
    },
    info: {
      title: "Информация",
      free: "🆓 Совершенно бесплатно",
      noRegistration: "🚀 Без регистрации",
      noWatermarks: "⭐ Без водяных знаков",
      privacy: "🔒 Конфиденциальность прежде всего",
      madeWithLove: "💖 Сделано с любовью"
    },
    copyright: "Все права защищены",
    madeWith: "Сделано с",
    freeForever: "Бесплатно навсегда",
    links: {
      privacy: "Политика конфиденциальности",
      terms: "Условия использования",
      contact: "Контакты"
    }
  },
  errors: {
    notFound: {
      title: "404 Страница не найдена",
      description: "Вы забыли добавить страницу в роутер?"
    }
  },
  defaults: {
    sampleText: "БРАТ ЭНЕРГИЯ"
  }
};

// Translation dictionaries
const translations: Record<Language, Translations> = {
  en: enTranslations,
  ja: jaTranslations,
  ru: ruTranslations,
  es: enTranslations, // Temporarily use English as fallback
  ko: enTranslations, // Temporarily use English as fallback  
  de: enTranslations, // Temporarily use English as fallback
};

// Default language
export const DEFAULT_LANGUAGE: Language = 'en';

// Get translations for a specific language with fallback and JSON support
export function getTranslations(language: Language): Translations {
  // First try to get from the main translations object
  let translation = translations[language];

  // If not found or is fallback, try to get from JSON translations
  if (!translation || translation === translations[DEFAULT_LANGUAGE]) {
    const jsonTranslation = getJsonTranslations(language);
    if (jsonTranslation) {
      // Convert JSON translation to match Translations interface
      const convertedTranslation = convertJsonToTranslations(jsonTranslation);
      if (convertedTranslation) {
        translation = convertedTranslation;
      }
    }
  }

  const fallback = translations[DEFAULT_LANGUAGE];
  return translation || fallback;
}

// Helper function to get JSON-based translations for new languages
export function getJsonTranslations(language: Language): any {
  try {
    switch (language) {
      case 'es':
        return esTranslations;
      case 'ko':
        return koTranslations;
      case 'de':
        return deTranslations;
      default:
        return null;
    }
  } catch (error) {
    return null;
  }
}

// Convert JSON translation to match Translations interface
function convertJsonToTranslations(jsonTranslation: any): Translations | null {
  try {
    // Map JSON structure to Translations interface
    return {
      nav: {
        home: jsonTranslation.nav?.home || "Home",
        generator: jsonTranslation.nav?.generator || jsonTranslation.controls?.title || "Generator",
        tutorial: jsonTranslation.nav?.tutorial || jsonTranslation.tutorial?.title || "Tutorial",
        about: jsonTranslation.nav?.about || jsonTranslation.nav?.faq || "About",
        menu: jsonTranslation.nav?.menu || "Menu"
      },
      header: {
        title: jsonTranslation.hero?.title || jsonTranslation.header?.title || "BratForge",
        subtitle: jsonTranslation.hero?.description || jsonTranslation.header?.subtitle || "",
        features: {
          realtime: jsonTranslation.header?.features?.realtime || "⚡ Real-time Preview",
          colors: jsonTranslation.header?.features?.colors || "🎨 Custom Colors",
          effects: jsonTranslation.header?.features?.effects || "🖊️ Scribble Effects",
          download: jsonTranslation.header?.features?.download || "💾 PNG Download",
          mobile: jsonTranslation.header?.features?.mobile || "📱 Mobile Friendly"
        }
      },
      intro: {
        title: jsonTranslation.hero?.subtitle || jsonTranslation.features?.title || "",
        description: jsonTranslation.hero?.description || jsonTranslation.features?.subtitle || ""
      },
      whyChoose: {
        title: jsonTranslation.features?.title || "Why Choose BratForge?",
        instantResults: {
          title: "Instant Results",
          description: "See your changes in real-time"
        },
        fullCustomization: {
          title: "Full Customization",
          description: "Complete control over colors and effects"
        },
        easyDownload: {
          title: "Easy Download",
          description: "High-quality PNG export"
        }
      },
      quickLinks: {
        title: "Quick Links",
        textGenerator: "Text Generator",
        tutorial: "Tutorial",
        aboutUs: "About Us"
      },
      brand: {
        description: jsonTranslation.hero?.description || ""
      },
      faq: {
        title: jsonTranslation.faq?.title || "FAQ",
        items: jsonTranslation.faq?.items || []
      },
      about: {
        title: "About BRAT TEXT GENERATOR",
        ourMission: {
          title: "Our Mission",
          description: "We believe everyone deserves to express their inner brat with style. Our generator makes it easy to create bold, eye-catching text graphics that capture that perfect mix of confidence and rebellion."
        },
        whyWeBuilt: {
          title: "Why We Built This",
          description: "Tired of boring, generic text tools? So were we. We created this generator specifically for the brat aesthetic - vibrant colors, bold fonts, and that unmistakable edge that makes your content stand out."
        },
        completelyFree: {
          title: "Completely Free",
          description: "No hidden fees, no watermarks, no limits. Create as many designs as you want, whenever you want. We believe great design tools should be accessible to everyone."
        },
        builtForEveryone: {
          title: "Built for Everyone",
          description: "Whether you're a social media influencer, content creator, or just someone who loves bold design, our tool is designed to be intuitive and powerful for users of all skill levels."
        }
      },
      styles: {
        classicBrat: {
          title: jsonTranslation.controls?.presets?.bratClassic || "🖊️ Classic Brat",
          description: "Bold and rebellious text style",
          tags: {
            limeGreen: "Lime Green",
            boldText: "Bold Text",
            cleanDesign: "Clean Design"
          }
        },
        scribbleEffect: {
          title: "Scribble Effect",
          description: "Chaotic and edgy text style",
          tags: {
            chaoticLines: "Chaotic Lines",
            edgyVibes: "Edgy Vibes",
            customizable: "Customizable"
          }
        }
      },
      controls: {
        textInput: {
          label: jsonTranslation.controls?.textInput?.label || "Text Input",
          placeholder: jsonTranslation.controls?.textInput?.placeholder || "Enter your text here..."
        },
        quickStyles: jsonTranslation.controls?.quickStyles || "Quick Styles",
        presets: {
          bratClassic: jsonTranslation.controls?.presets?.bratClassic || "🖊️ Classic Brat",
          hotPink: jsonTranslation.controls?.presets?.hotPink || "💖 Hot Pink",
          neonGlow: jsonTranslation.controls?.presets?.neonGlow || "⚡ Neon Glow",
          royalPurple: jsonTranslation.controls?.presets?.royalPurple || "👑 Royal Purple"
        },
        colors: {
          title: jsonTranslation.controls?.colors?.title || "Colors",
          background: jsonTranslation.controls?.colors?.background || "Background",
          textColor: jsonTranslation.controls?.colors?.textColor || "Text Color",
          scribbleColor: jsonTranslation.controls?.colors?.scribbleColor || "Scribble Color"
        },
        textSettings: {
          title: jsonTranslation.controls?.textSettings?.title || "Text Settings",
          fontSize: jsonTranslation.controls?.textSettings?.fontSize || "Font Size",
          alignment: jsonTranslation.controls?.textSettings?.alignment || "Alignment",
          alignments: {
            left: jsonTranslation.controls?.textSettings?.alignments?.left || "Left",
            center: jsonTranslation.controls?.textSettings?.alignments?.center || "Center",
            right: jsonTranslation.controls?.textSettings?.alignments?.right || "Right",
            justify: jsonTranslation.controls?.textSettings?.alignments?.justify || "Justify"
          }
        },
        effects: {
          title: jsonTranslation.controls?.effects?.title || "Effects",
          blur: jsonTranslation.controls?.effects?.blur || "Blur Effect",
          scribble: jsonTranslation.controls?.effects?.scribble || "Scribble Effect",
          intensity: jsonTranslation.controls?.effects?.intensity || "Scribble Intensity",
          light: jsonTranslation.controls?.effects?.light || "Light",
          heavy: jsonTranslation.controls?.effects?.heavy || "Heavy",
          off: jsonTranslation.controls?.effects?.off || "OFF"
        },
        transforms: {
          title: jsonTranslation.controls?.transforms?.title || "TEXT TRANSFORMATION",
          flipMirror: jsonTranslation.controls?.transforms?.flipMirror || "Flip & Mirror",
          flipHorizontal: jsonTranslation.controls?.transforms?.flipHorizontal || "Flip Horizontal",
          flipVertical: jsonTranslation.controls?.transforms?.flipVertical || "Flip Vertical",
          rotation: jsonTranslation.controls?.transforms?.rotation || "Rotation",
          rotationLabel: jsonTranslation.controls?.transforms?.rotationLabel || "Rotation: 0°",
          quickTransform: jsonTranslation.controls?.transforms?.quickTransform || "Quick Transform",
          presets: {
            mirror: jsonTranslation.controls?.transforms?.presets?.mirror || "🪞 Mirror",
            flip: jsonTranslation.controls?.transforms?.presets?.flip || "🔄 Flip",
            upsideDown: jsonTranslation.controls?.transforms?.presets?.upsideDown || "🙃 Upside Down",
            reset: jsonTranslation.controls?.transforms?.presets?.reset || "🔄 Reset"
          }
        },
        spacing: {
          title: "Spacing",
          letterSpacing: "Letter Spacing",
          wordSpacing: "Word Spacing",
          tight: "Tight",
          wide: "Wide",
          close: "Close",
          far: "Far",
          presets: {
            title: "Spacing Presets",
            wideOpen: "Wide Open",
            extraWide: "Extra Wide",
            tight: "Tight",
            normal: "Normal"
          }
        },
        download: jsonTranslation.download?.button || "Download PNG"
      },
      canvas: {
        preview: "Preview"
      },
      download: {
        button: jsonTranslation.download?.button || "Download PNG"
      },
      tutorial: {
        title: jsonTranslation.tutorial?.title || "Tutorial",
        description: jsonTranslation.tutorial?.description || "",
        detailedSteps: {
          step1: {
            title: jsonTranslation.tutorial?.detailedSteps?.step1?.title || "1. Enter Your Text",
            description: jsonTranslation.tutorial?.detailedSteps?.step1?.description || ""
          },
          step2: {
            title: jsonTranslation.tutorial?.detailedSteps?.step2?.title || "2. Choose Color Scheme",
            description: jsonTranslation.tutorial?.detailedSteps?.step2?.description || ""
          },
          step3: {
            title: jsonTranslation.tutorial?.detailedSteps?.step3?.title || "3. Adjust Font Size",
            description: jsonTranslation.tutorial?.detailedSteps?.step3?.description || ""
          },
          step4: {
            title: jsonTranslation.tutorial?.detailedSteps?.step4?.title || "4. Set Text Alignment",
            description: jsonTranslation.tutorial?.detailedSteps?.step4?.description || ""
          },
          step5: {
            title: jsonTranslation.tutorial?.detailedSteps?.step5?.title || "5. Apply Effects",
            description: jsonTranslation.tutorial?.detailedSteps?.step5?.description || ""
          },
          step6: {
            title: jsonTranslation.tutorial?.detailedSteps?.step6?.title || "6. Transform Text",
            description: jsonTranslation.tutorial?.detailedSteps?.step6?.description || ""
          },
          step7: {
            title: jsonTranslation.tutorial?.detailedSteps?.step7?.title || "7. Download Your Creation",
            description: jsonTranslation.tutorial?.detailedSteps?.step7?.description || ""
          }
        },
        steps: {
          step1: {
            title: jsonTranslation.tutorial?.steps?.step1?.title || "Enter Text",
            description: jsonTranslation.tutorial?.steps?.step1?.description || ""
          },
          step2: {
            title: jsonTranslation.tutorial?.steps?.step2?.title || "Choose Colors",
            description: jsonTranslation.tutorial?.steps?.step2?.description || ""
          },
          step3: {
            title: jsonTranslation.tutorial?.steps?.step3?.title || "Add Effects",
            description: jsonTranslation.tutorial?.steps?.step3?.description || ""
          },
          step4: {
            title: jsonTranslation.tutorial?.steps?.step4?.title || "Download & Share",
            description: jsonTranslation.tutorial?.steps?.step4?.description || ""
          }
        },
        tips: {
          title: jsonTranslation.tutorial?.tips?.title || "Pro Tips",
          tip1: jsonTranslation.tutorial?.tips?.tip1 || "",
          tip2: jsonTranslation.tutorial?.tips?.tip2 || "",
          tip3: jsonTranslation.tutorial?.tips?.tip3 || "",
          tip4: jsonTranslation.tutorial?.tips?.tip4 || ""
        },
        howToUse: {
          title: jsonTranslation.tutorial?.howToUse?.title || "How to Use"
        },
        detailsList: {
          step1: ["Real-time text input with instant preview"],
          step2: ["Custom color pickers with hex code support"],
          step3: ["Font size control from 12px to 120px"],
          step4: ["Multiple text alignment options"],
          step5: ["Unique blur and scribble effects"],
          step6: ["Advanced text transformation tools"],
          step7: ["High-quality PNG download"]
        },
        tipsList: [
          "Use high contrast colors for maximum impact",
          "Short phrases work better than long sentences",
          "Experiment with different effects for unique looks",
          "Scribble effects add authentic brat touch"
        ],
        descriptionText: jsonTranslation.tutorial?.description || "",
        proTipsTitle: jsonTranslation.tutorial?.tips?.title || "Pro Tips",
        styleExamples: {
          title: "Style Examples",
          gradientBold: "Gradient Bold",
          neonBlur: "Neon Blur",
          glowingText: "Glowing Text",
          scribbleEffect: "Scribble Effect"
        }
      },
      footer: {
        quickLinks: {
          title: jsonTranslation.footer?.quickLinks || "Quick Links"
        },
        features: {
          title: jsonTranslation.footer?.features || "Features",
          realtime: "⚡ Real-time Preview",
          customColors: "🎨 Custom Colors",
          scribbleEffects: "🖊️ Scribble Effects",
          pngDownload: "💾 PNG Download",
          mobileFriendly: "📱 Mobile Friendly"
        },
        info: {
          title: jsonTranslation.footer?.support || "Info",
          free: "🆓 Totally Free",
          noRegistration: "🚀 No Registration",
          noWatermarks: "⭐ No Watermarks",
          privacy: "🔒 Privacy First",
          madeWithLove: "💖 Made with Love"
        },
        copyright: jsonTranslation.footer?.copyright || "All rights reserved",
        madeWith: "Made with",
        freeForever: "Free forever",
        links: {
          privacy: jsonTranslation.footer?.links?.privacy || "Privacy Policy",
          terms: jsonTranslation.footer?.links?.terms || "Terms of Service",
          contact: jsonTranslation.footer?.links?.contact || "Contact"
        }
      },
      errors: {
        notFound: {
          title: "404 Page Not Found",
          description: "Did you forget to add the page to the router?"
        }
      },
      defaults: {
        sampleText: jsonTranslation.defaults?.sampleText || "BRAT ENERGY"
      }
    };
  } catch (error) {
    console.error('Error converting JSON translation:', error);
    return null;
  }
}

// Language display names
export const languageNames: Record<Language, string> = {
  en: 'English',
  ja: '日本語',
  ru: 'Русский',
  es: 'Español',
  ko: '한국어',
  de: 'Deutsch',
};