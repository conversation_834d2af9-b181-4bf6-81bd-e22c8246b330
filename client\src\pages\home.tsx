import { TextGenerator } from "@/components/text-generator";
import { LanguageSwitcher } from "@/components/language-switcher";
import { TutorialSection } from "@/components/tutorial-section";
import { FAQSection } from "@/components/faq-section";
import { useLanguage } from "@/hooks/use-language";
import { usePageSEO } from "@/hooks/use-seo";
import bratTextClean from "@assets/brat-text (2)_1754976682645.png";
import bratTextScribble from "@assets/brat-text_1754976785470.png";

export default function Home() {
  const { t } = useLanguage();
  usePageSEO(); // Apply SEO meta tags dynamically
  
  // Add a safety check for translations
  if (!t || !t.footer || !t.footer.quickLinks) {
    return <div className="gradient-bg min-h-screen flex items-center justify-center text-white">Loading...</div>;
  }
  
  return (
    <div className="gradient-bg min-h-screen text-white font-brat">
      {/* Modern Navigation Bar */}
      <nav className="sticky top-0 z-50 glass-card">
        <div className="max-w-7xl mx-auto px-6 sm:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Enhanced Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-brat-lime to-brat-pink rounded-lg flex items-center justify-center">
                <span className="text-black font-black text-lg">B</span>
              </div>
              <div>
                <span className="text-2xl font-black text-brat-lime glow-text" data-testid="nav-logo">
                  Brat
                </span>
                <span className="text-xl font-bold text-white ml-1">Forge</span>
              </div>
            </div>
            
            {/* Enhanced Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              <a href="#home" className="nav-link text-brat-lime" data-testid="nav-home">
                {t.nav.home}
              </a>
              <a href="#generator" className="nav-link hover:text-brat-lime" data-testid="nav-generator-link">
                {t.nav.generator}
              </a>
              <a href="#tutorial" className="nav-link hover:text-brat-pink" data-testid="nav-tutorial-link">
                {t.nav.tutorial}
              </a>
              <a href="#about" className="nav-link hover:text-brat-cyan" data-testid="nav-about">
                {t.nav.about}
              </a>
              <div className="ml-4 pl-4 border-l border-white/20">
                <LanguageSwitcher />
              </div>
            </div>

            {/* Mobile Navigation */}
            <div className="md:hidden flex items-center space-x-3">
              <LanguageSwitcher />
              <button className="glass-card px-4 py-2 rounded-lg font-bold hover:bg-brat-lime/20 transition-all duration-300" data-testid="nav-mobile-menu">
                {t.nav.menu}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section with Enhanced Design */}
      <section id="home" className="relative py-20 px-6 text-center hero-gradient">
        <div className="max-w-6xl mx-auto">
          {/* Main Hero Content */}
          <div className="mb-12">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-transparent bg-gradient-to-r from-brat-lime via-brat-pink to-brat-cyan bg-clip-text mb-6 floating-animation">
              {t.header.title}
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto mb-10 leading-relaxed">
              {t.header.subtitle}
            </p>
          </div>

          {/* Feature Pills - Enhanced Design */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-12">
            <div className="glass-card p-4 rounded-2xl border-l-4 border-brat-lime hover:scale-105 transition-transform duration-300">
              <div className="text-brat-lime text-2xl mb-2">⚡</div>
              <span className="text-brat-lime font-bold text-sm">{t.header.features.realtime}</span>
            </div>
            <div className="glass-card p-4 rounded-2xl border-l-4 border-brat-pink hover:scale-105 transition-transform duration-300">
              <div className="text-brat-pink text-2xl mb-2">🎨</div>
              <span className="text-brat-pink font-bold text-sm">{t.header.features.colors}</span>
            </div>
            <div className="glass-card p-4 rounded-2xl border-l-4 border-brat-cyan hover:scale-105 transition-transform duration-300">
              <div className="text-brat-cyan text-2xl mb-2">✨</div>
              <span className="text-brat-cyan font-bold text-sm">{t.header.features.effects}</span>
            </div>
            <div className="glass-card p-4 rounded-2xl border-l-4 border-brat-lime hover:scale-105 transition-transform duration-300">
              <div className="text-brat-lime text-2xl mb-2">📥</div>
              <span className="text-brat-lime font-bold text-sm">{t.header.features.download}</span>
            </div>
            <div className="glass-card p-4 rounded-2xl border-l-4 border-brat-pink hover:scale-105 transition-transform duration-300 col-span-2 md:col-span-1">
              <div className="text-brat-pink text-2xl mb-2">📱</div>
              <span className="text-brat-pink font-bold text-sm">{t.header.features.mobile}</span>
            </div>
          </div>

          {/* Enhanced CTA Section */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <a 
              href="#generator" 
              className="group px-8 py-4 bg-gradient-to-r from-brat-lime to-brat-pink text-black font-bold rounded-2xl hover:scale-105 transition-all duration-300 pulse-glow shadow-2xl"
              data-testid="nav-generator"
            >
              <span className="flex items-center space-x-3">
                <span className="text-2xl">🎨</span>
                <span className="text-lg">{t.nav.generator}</span>
                <span className="group-hover:translate-x-1 transition-transform">→</span>
              </span>
            </a>
            <a 
              href="#tutorial" 
              className="px-6 py-3 bg-transparent border-2 border-brat-pink text-brat-pink font-bold rounded-2xl hover:bg-brat-pink hover:text-black transition-all duration-300"
              data-testid="nav-tutorial"
            >
              📖 {t.nav.tutorial}
            </a>
            <a 
              href="#about" 
              className="px-6 py-3 bg-transparent border-2 border-brat-cyan text-brat-cyan font-bold rounded-2xl hover:bg-brat-cyan hover:text-black transition-all duration-300"
              data-testid="nav-faq"
            >
              ❓ {t.nav.about}
            </a>
          </div>
        </div>
      </section>

      {/* Introduction Section with Modern Design */}
      <section className="py-16 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black text-transparent bg-gradient-to-r from-brat-lime via-brat-pink to-brat-cyan bg-clip-text mb-6">
              {t.intro.title}
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              {t.intro.description}
            </p>
          </div>

          {/* Style Showcase Cards */}
          <div className="grid lg:grid-cols-2 gap-8 mb-16">
            {/* Classic Brat Style Card */}
            <div className="glass-card rounded-3xl p-8 hover:scale-[1.02] transition-all duration-500">
              <div className="mb-8">
                <img 
                  src={bratTextClean} 
                  alt="Classic Brat Text - Clean lime green background with bold black text"
                  className="w-full rounded-2xl shadow-2xl border border-brat-lime/30"
                  data-testid="img-classic-brat"
                />
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-black text-brat-lime">{t.styles.classicBrat.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {t.styles.classicBrat.description}
                </p>
                <div className="flex flex-wrap gap-3">
                  <span className="bg-gradient-to-r from-brat-lime to-brat-lime/80 text-black px-4 py-2 rounded-full text-sm font-bold">
                    {t.styles.classicBrat.tags.limeGreen}
                  </span>
                  <span className="bg-gray-800 text-white px-4 py-2 rounded-full text-sm font-bold">
                    {t.styles.classicBrat.tags.boldText}
                  </span>
                  <span className="bg-gradient-to-r from-brat-pink to-brat-pink/80 text-white px-4 py-2 rounded-full text-sm font-bold">
                    {t.styles.classicBrat.tags.cleanDesign}
                  </span>
                </div>
              </div>
            </div>

            {/* Scribble Brat Style Card */}
            <div className="glass-card rounded-3xl p-8 hover:scale-[1.02] transition-all duration-500">
              <div className="mb-8">
                <img 
                  src={bratTextScribble} 
                  alt="Scribble Brat Text - Creative scribbled effect with colorful design"
                  className="w-full rounded-2xl shadow-2xl border border-brat-pink/30"
                  data-testid="img-scribble-brat"
                />
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-black text-brat-pink">{t.styles.scribbleEffect.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {t.styles.scribbleEffect.description}
                </p>
                <div className="flex flex-wrap gap-3">
                  <span className="bg-gradient-to-r from-brat-pink to-brat-pink/80 text-white px-4 py-2 rounded-full text-sm font-bold">
                    {t.styles.scribbleEffect.tags.chaoticLines}
                  </span>
                  <span className="bg-gradient-to-r from-brat-cyan to-brat-cyan/80 text-black px-4 py-2 rounded-full text-sm font-bold">
                    {t.styles.scribbleEffect.tags.edgyVibes}
                  </span>
                  <span className="bg-gray-800 text-white px-4 py-2 rounded-full text-sm font-bold">
                    {t.styles.scribbleEffect.tags.customizable}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Features Section */}
          <div className="glass-card rounded-3xl p-8 border-l-4 border-brat-lime">
            <h3 className="text-3xl font-black text-transparent bg-gradient-to-r from-brat-lime to-brat-cyan bg-clip-text mb-8 text-center">
              {t.whyChoose.title}
            </h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-brat-lime to-brat-lime/70 rounded-2xl flex items-center justify-center mx-auto">
                  <span className="text-3xl">⚡</span>
                </div>
                <h4 className="text-xl font-bold text-brat-lime">{t.whyChoose.instantResults.title}</h4>
                <p className="text-gray-300 leading-relaxed">{t.whyChoose.instantResults.description}</p>
              </div>
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-brat-pink to-brat-pink/70 rounded-2xl flex items-center justify-center mx-auto">
                  <span className="text-3xl">🎨</span>
                </div>
                <h4 className="text-xl font-bold text-brat-pink">{t.whyChoose.fullCustomization.title}</h4>
                <p className="text-gray-300 leading-relaxed">{t.whyChoose.fullCustomization.description}</p>
              </div>
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-brat-cyan to-brat-cyan/70 rounded-2xl flex items-center justify-center mx-auto">
                  <span className="text-3xl">💾</span>
                </div>
                <h4 className="text-xl font-bold text-brat-cyan">{t.whyChoose.easyDownload.title}</h4>
                <p className="text-gray-300 leading-relaxed">{t.whyChoose.easyDownload.description}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Modern About Section */}
      <section id="about" className="py-20 px-6 relative">
        <div className="hero-gradient absolute inset-0 opacity-50"></div>
        <div className="relative max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black text-transparent bg-gradient-to-r from-brat-cyan via-brat-pink to-brat-lime bg-clip-text mb-6">
              {t.about.title}
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div className="space-y-8">
              <div className="glass-card p-8 rounded-3xl border-l-4 border-brat-lime">
                <h3 className="text-2xl font-black text-brat-lime mb-4">{t.about.ourMission.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {t.about.ourMission.description}
                </p>
              </div>
              <div className="glass-card p-8 rounded-3xl border-l-4 border-brat-pink">
                <h3 className="text-2xl font-black text-brat-pink mb-4">{t.about.whyWeBuilt.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {t.about.whyWeBuilt.description}
                </p>
              </div>
            </div>
            <div className="space-y-8">
              <div className="glass-card p-8 rounded-3xl border-l-4 border-brat-violet">
                <h3 className="text-2xl font-black text-brat-violet mb-4">{t.about.completelyFree.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {t.about.completelyFree.description}
                </p>
              </div>
              <div className="glass-card p-8 rounded-3xl border-l-4 border-brat-cyan">
                <h3 className="text-2xl font-black text-brat-cyan mb-4">{t.about.builtForEveryone.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {t.about.builtForEveryone.description}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div id="generator">
        <TextGenerator />
      </div>

      {/* Tutorial and FAQ sections moved inline */}
      <section id="tutorial" className="py-20 px-6 relative bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black text-transparent bg-gradient-to-r from-brat-lime via-brat-pink to-brat-cyan bg-clip-text mb-6">
              {t.tutorial.title}
            </h2>
            <p className="text-gray-400 text-xl max-w-3xl mx-auto leading-relaxed">
              {t.tutorial.description}
            </p>
          </div>
        </div>
      </section>

      {/* Modern Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-transparent py-20 px-6">
        <div className="max-w-6xl mx-auto">
          {/* Main Footer Content */}
          <div className="grid md:grid-cols-4 gap-12 mb-12">
            {/* Enhanced Brand Section */}
            <div className="md:col-span-2 space-y-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-brat-lime to-brat-pink rounded-xl flex items-center justify-center">
                  <span className="text-black font-black text-xl">B</span>
                </div>
                <div>
                  <span className="text-3xl font-black text-brat-lime glow-text">Brat</span>
                  <span className="text-2xl font-bold text-white ml-1">Forge</span>
                </div>
              </div>
              <p className="text-gray-400 leading-relaxed text-lg max-w-md">
                {t.brand.description}
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center hover:bg-brat-lime/20 transition-colors cursor-pointer">
                  <span className="text-brat-lime">📧</span>
                </div>
                <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center hover:bg-brat-pink/20 transition-colors cursor-pointer">
                  <span className="text-brat-pink">🐦</span>
                </div>
                <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center hover:bg-brat-cyan/20 transition-colors cursor-pointer">
                  <span className="text-brat-cyan">📱</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-6">
              <h3 className="text-xl font-black text-brat-pink">{t.footer.quickLinks.title}</h3>
              <div className="space-y-4">
                <a href="#generator" className="footer-link">{t.nav.generator}</a>
                <a href="#tutorial" className="footer-link">{t.nav.tutorial}</a>
                <a href="#about" className="footer-link">{t.nav.about}</a>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-6">
              <h3 className="text-xl font-black text-brat-cyan">{t.footer.features.title}</h3>
              <div className="space-y-4">
                <p className="footer-link">{t.header.features.realtime}</p>
                <p className="footer-link">{t.header.features.colors}</p>
                <p className="footer-link">{t.header.features.effects}</p>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t border-white/10 pt-8 text-center">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <p className="text-gray-500 text-sm">
                © 2025 BratForge. {t.footer.copyright}
              </p>
              <div className="flex items-center space-x-6">
                <LanguageSwitcher />
                <p className="text-gray-500 text-sm">
                  {t.footer.madeWith} ❤️
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>

    </div>
  );
}
