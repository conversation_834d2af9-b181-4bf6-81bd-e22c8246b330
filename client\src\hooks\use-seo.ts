import { useEffect } from 'react';
import { useLanguage } from './use-language';

interface SEOConfig {
  title: string;
  description: string;
  keywords: string;
  ogTitle?: string;
  ogDescription?: string;
  twitterTitle?: string;
  twitterDescription?: string;
}

export const useSEO = (config: SEOConfig) => {
  useEffect(() => {
    // Update document title
    document.title = config.title;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', config.description);
    }

    // Update meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
      metaKeywords.setAttribute('content', config.keywords);
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute('content', config.ogTitle || config.title);
    }

    const ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
      ogDescription.setAttribute('content', config.ogDescription || config.description);
    }

    // Update Twitter tags
    const twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitle) {
      twitterTitle.setAttribute('content', config.twitterTitle || config.title);
    }

    const twitterDescription = document.querySelector('meta[name="twitter:description"]');
    if (twitterDescription) {
      twitterDescription.setAttribute('content', config.twitterDescription || config.description);
    }

    // Update HTML lang attribute based on keywords
    if (config.keywords.includes('ブラット')) {
      document.documentElement.lang = 'ja';
    } else if (config.keywords.includes('генератор')) {
      document.documentElement.lang = 'ru';
    } else {
      document.documentElement.lang = 'en';
    }

  }, [config]);
};

export const usePageSEO = () => {
  const { t, language } = useLanguage();

  const seoConfig: SEOConfig = {
    title: (t as any).seo?.title || `BratForge - ${language === 'ja' ? '無料ブラットテキストジェネレーター' : language === 'ru' ? 'Бесплатный генератор брат-текста' : 'Free Brat Text Generator'}`,
    description: (t as any).seo?.description || (t as any).brand?.description || '',
    keywords: (t as any).seo?.keywords || '',
    ogTitle: (t as any).seo?.title,
    ogDescription: (t as any).seo?.description,
    twitterTitle: (t as any).seo?.title,
    twitterDescription: (t as any).seo?.description
  };

  useSEO(seoConfig);

  return seoConfig;
};