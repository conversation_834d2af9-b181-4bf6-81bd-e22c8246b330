import { useCallback, useRef } from "react";
import type { TextSettings } from "@/components/text-generator";

export function useCanvas(canvasRef: React.RefObject<HTMLCanvasElement>, settings: TextSettings) {
  const updateCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set up high DPI rendering
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = 600 * dpr;
    canvas.height = 300 * dpr;
    ctx.scale(dpr, dpr);
    canvas.style.width = '600px';
    canvas.style.height = '300px';

    // Clear canvas
    ctx.clearRect(0, 0, 600, 300);
    
    // Set background
    ctx.fillStyle = settings.backgroundColor;
    ctx.fillRect(0, 0, 600, 300);
    
    // Save the canvas state before transformations
    ctx.save();
    
    // Apply canvas transformations for flip and rotation
    const centerX = 300;
    const centerY = 150;
    
    // Move to center for transformations
    ctx.translate(centerX, centerY);
    
    // Apply rotation
    if (settings.rotation !== 0) {
      ctx.rotate((settings.rotation * Math.PI) / 180);
    }
    
    // Apply flips
    const scaleX = settings.flipHorizontal ? -1 : 1;
    const scaleY = settings.flipVertical ? -1 : 1;
    ctx.scale(scaleX, scaleY);
    
    // Move back from center
    ctx.translate(-centerX, -centerY);
    
    // Set text properties
    ctx.font = `900 ${settings.fontSize}px Inter, Arial, sans-serif`;
    ctx.fillStyle = settings.textColor;
    ctx.textBaseline = 'middle';
    
    // Apply blur effect
    if (settings.blurIntensity > 0) {
      ctx.filter = `blur(${settings.blurIntensity}px)`;
    } else {
      ctx.filter = 'none';
    }
    
    // Set text alignment
    ctx.textAlign = settings.alignment === 'justify' ? 'left' : settings.alignment;
    
    // Calculate position based on alignment
    let x: number;
    switch (settings.alignment) {
      case 'center':
        x = 300;
        break;
      case 'right':
        x = 580;
        break;
      case 'justify':
      case 'left':
      default:
        x = 20;
    }
    
    // Draw text with custom spacing and word wrapping
    const words = settings.text.split(' ');
    const lineHeight = settings.fontSize * 1.2;
    const maxWidth = 560;
    let y = 150;
    const textLines: { words: string[]; x: number; y: number; width: number }[] = [];
    
    // Helper function to measure text with custom spacing
    const measureTextWithSpacing = (text: string) => {
      let width = 0;
      for (let i = 0; i < text.length; i++) {
        const charWidth = ctx.measureText(text[i]).width;
        width += charWidth;
        if (i < text.length - 1) {
          width += settings.letterSpacing;
        }
      }
      return width;
    };
    
    // Helper function to measure line with word spacing
    const measureLineWithSpacing = (words: string[]) => {
      let totalWidth = 0;
      for (let i = 0; i < words.length; i++) {
        totalWidth += measureTextWithSpacing(words[i]);
        if (i < words.length - 1) {
          totalWidth += ctx.measureText(' ').width + settings.wordSpacing;
        }
      }
      return totalWidth;
    };
    
    // Word wrapping with spacing considerations
    let currentLineWords: string[] = [];
    for (let i = 0; i < words.length; i++) {
      const testWords = [...currentLineWords, words[i]];
      const testWidth = measureLineWithSpacing(testWords);
      
      if (testWidth > maxWidth && currentLineWords.length > 0) {
        // Add current line and start new one
        const lineWidth = measureLineWithSpacing(currentLineWords);
        textLines.push({ words: [...currentLineWords], x, y, width: lineWidth });
        currentLineWords = [words[i]];
        y += lineHeight;
      } else {
        currentLineWords.push(words[i]);
      }
    }
    
    // Add the last line
    if (currentLineWords.length > 0) {
      const lineWidth = measureLineWithSpacing(currentLineWords);
      textLines.push({ words: [...currentLineWords], x, y, width: lineWidth });
    }
    
    // Draw each line with custom spacing
    textLines.forEach(line => {
      let currentX = line.x;
      
      // Adjust starting position based on alignment
      if (settings.alignment === 'center') {
        currentX = line.x - line.width / 2;
      } else if (settings.alignment === 'right') {
        currentX = line.x - line.width;
      }
      
      // Draw each word with custom letter and word spacing
      line.words.forEach((word, wordIndex) => {
        // Draw each character with custom letter spacing
        for (let i = 0; i < word.length; i++) {
          const char = word[i];
          ctx.fillText(char, currentX, line.y);
          const charWidth = ctx.measureText(char).width;
          currentX += charWidth + settings.letterSpacing;
        }
        
        // Add word spacing (except for last word)
        if (wordIndex < line.words.length - 1) {
          const spaceWidth = ctx.measureText(' ').width;
          currentX += spaceWidth + settings.wordSpacing;
        }
      });
    });

    // Draw scribble effect if enabled
    if (settings.scribbleEffect) {
      ctx.filter = 'none'; // Remove blur for scribbles
      ctx.strokeStyle = settings.scribbleColor;
      ctx.lineWidth = 2;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      
      // Draw scribbles over each line of text
      textLines.forEach(textLine => {
        const textWidth = textLine.width;
        let startX = textLine.x;
        
        // Adjust starting position based on alignment
        if (settings.alignment === 'center') {
          startX = textLine.x - textWidth / 2;
        } else if (settings.alignment === 'right') {
          startX = textLine.x - textWidth;
        }
        
        const endX = startX + textWidth;
        const centerY = textLine.y;
        
        // Generate random scribble lines
        for (let i = 0; i < settings.scribbleIntensity; i++) {
          ctx.beginPath();
          
          // Random starting point around the text
          const startXPos = startX + (Math.random() - 0.5) * textWidth * 1.2;
          const startYPos = centerY + (Math.random() - 0.5) * settings.fontSize * 0.8;
          
          ctx.moveTo(startXPos, startYPos);
          
          // Create wavy scribble line
          const segments = 5 + Math.random() * 5;
          for (let j = 0; j < segments; j++) {
            const progress = j / segments;
            const xPos = startXPos + (endX - startXPos) * progress + (Math.random() - 0.5) * 20;
            const yPos = startYPos + (Math.random() - 0.5) * 15;
            ctx.lineTo(xPos, yPos);
          }
          
          ctx.stroke();
        }
      });
    }
    
    // Restore the canvas state
    ctx.restore();
  }, [canvasRef, settings]);

  const downloadImage = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.toBlob((blob) => {
      if (!blob) return;
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'brat-text.png';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 'image/png');
  }, [canvasRef]);

  return { updateCanvas, downloadImage };
}
