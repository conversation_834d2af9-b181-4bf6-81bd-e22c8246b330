import { Globe } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLanguage } from "@/hooks/use-language";
import { Language, languageNames } from "@/lib/i18n";

export function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="bg-transparent border-brat-lime/30 text-white hover:bg-brat-lime/10 hover:text-brat-lime transition-colors"
          data-testid="language-switcher-trigger"
        >
          <Globe className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">{languageNames[language]}</span>
          <span className="sm:hidden">{language.toUpperCase()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="bg-brat-gray border-brat-lime/30"
        data-testid="language-switcher-menu"
      >
        {(Object.keys(languageNames) as Language[]).map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => handleLanguageChange(lang)}
            className={`text-white hover:bg-brat-lime/20 focus:bg-brat-lime/20 cursor-pointer ${
              language === lang ? 'bg-brat-lime/10 text-brat-lime' : ''
            }`}
            data-testid={`language-option-${lang}`}
          >
            <span className="text-sm font-medium">{languageNames[lang]}</span>
            {language === lang && (
              <span className="ml-auto text-brat-lime">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}