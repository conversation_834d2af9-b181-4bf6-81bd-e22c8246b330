import { useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { useCanvas } from "@/hooks/use-canvas";
import { useLanguage } from "@/hooks/use-language";
import type { TextSettings } from "./text-generator";

interface CanvasPreviewProps {
  settings: TextSettings;
}

export function CanvasPreview({ settings }: CanvasPreviewProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { updateCanvas, downloadImage } = useCanvas(canvasRef, settings);
  const { t } = useLanguage();

  useEffect(() => {
    updateCanvas();
  }, [settings, updateCanvas]);

  return (
    <div className="order-2 lg:order-1">
      <div className="bg-brat-gray rounded-2xl p-6 neon-border">
        <h2 className="text-2xl font-bold text-brat-cyan mb-4 flex items-center">
          <span className="w-3 h-3 bg-brat-lime rounded-full mr-3"></span>
          {t.canvas.preview}
        </h2>
        <div className="bg-black rounded-xl p-4 min-h-[300px] flex items-center justify-center">
          <canvas 
            ref={canvasRef}
            width="600" 
            height="300" 
            className="max-w-full h-auto border border-gray-700 rounded-lg"
            data-testid="canvas-preview"
          />
        </div>
        <Button 
          onClick={downloadImage}
          className="w-full mt-4 bg-gradient-to-r from-brat-pink to-brat-violet text-white font-bold py-4 px-6 rounded-xl hover:scale-105 transition-transform duration-200 text-lg"
          data-testid="button-download"
        >
          📥 {t.download.button}
        </Button>
      </div>
    </div>
  );
}
