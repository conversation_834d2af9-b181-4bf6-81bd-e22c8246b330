import { CanvasPreview } from "./canvas-preview";
import { ControlsPanel } from "./controls-panel";
import { TutorialSection } from "./tutorial-section";
import { FAQSection } from "./faq-section";
import { useLanguage } from "@/hooks/use-language";
import { useState } from "react";

export interface TextSettings {
  text: string;
  backgroundColor: string;
  textColor: string;
  fontSize: number;
  alignment: 'left' | 'center' | 'right' | 'justify';
  blurIntensity: number;
  scribbleEffect: boolean;
  scribbleIntensity: number;
  scribbleColor: string;
  flipHorizontal: boolean;
  flipVertical: boolean;
  rotation: number;
  letterSpacing: number;
  wordSpacing: number;
}

export function TextGenerator() {
  const { t } = useLanguage();
  
  const [settings, setSettings] = useState<TextSettings>({
    text: t.defaults.sampleText,
    backgroundColor: "#000000",
    textColor: "#BFFF00",
    fontSize: 48,
    alignment: "center",
    blurIntensity: 0,
    scribbleEffect: false,
    scribbleIntensity: 5,
    scribbleColor: "#000000",
    flipHorizontal: false,
    flipVertical: false,
    rotation: 0,
    letterSpacing: 0,
    wordSpacing: 0,
  });

  const updateSettings = (newSettings: Partial<TextSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  return (
    <>
      <main className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <CanvasPreview settings={settings} />
          <ControlsPanel settings={settings} onSettingsChange={updateSettings} t={t} />
        </div>
      </main>
      
      <TutorialSection />
      <FAQSection />
    </>
  );
}
