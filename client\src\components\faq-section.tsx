import { useLanguage } from "@/hooks/use-language";

export function FAQSection() {
  const { t } = useLanguage();

  return (
    <section id="faq" className="py-16 px-4">
      <div className="container mx-auto max-w-4xl">
        <h2 className="text-3xl md:text-5xl font-black text-center text-brat-pink glow-text mb-12">
          {t.faq.title}
        </h2>
        
        <div className="space-y-6">
          {t.faq.items.map((item, index) => (
            <div 
              key={index}
              className="bg-brat-gray rounded-xl p-6 border border-gray-700 hover:border-brat-lime transition-colors"
              data-testid={`faq-item-${index + 1}`}
            >
              <h3 className="text-brat-cyan font-bold text-lg mb-3">
                {index + 1}. {item.question}
              </h3>
              <p className="text-gray-300">{item.answer}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
