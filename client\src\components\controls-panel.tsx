import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import type { TextSettings } from "./text-generator";
import type { Translations } from "@/lib/i18n";

interface ControlsPanelProps {
  settings: TextSettings;
  onSettingsChange: (newSettings: Partial<TextSettings>) => void;
  t: Translations;
}

export function ControlsPanel({ settings, onSettingsChange, t }: ControlsPanelProps) {
  const handleColorChange = (type: 'backgroundColor' | 'textColor', value: string) => {
    onSettingsChange({ [type]: value });
  };

  const handleHexChange = (type: 'backgroundColor' | 'textColor', value: string) => {
    if (/^#[0-9A-F]{6}$/i.test(value)) {
      onSettingsChange({ [type]: value });
    }
  };

  const handleAlignmentChange = (alignment: TextSettings['alignment']) => {
    onSettingsChange({ alignment });
  };

  return (
    <div className="order-1 lg:order-2 space-y-6">
      {/* Text Input */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <Label className="block text-brat-lime font-bold text-lg mb-3">✍️ {t.controls.textInput.label.toUpperCase()}</Label>
        <Textarea 
          value={settings.text}
          onChange={(e) => onSettingsChange({ text: e.target.value })}
          placeholder={t.controls.textInput.placeholder}
          className="w-full bg-black border-2 border-brat-lime rounded-xl p-4 text-white font-semibold text-lg min-h-[100px] focus:border-brat-pink focus:outline-none transition-colors"
          data-testid="input-text"
        />
      </div>

      {/* Quick Presets */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <h3 className="text-brat-violet font-bold text-lg mb-4">⚡ {t.controls.quickStyles.toUpperCase()}</h3>
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={() => onSettingsChange({ 
              backgroundColor: "#BFFF00", 
              textColor: "#000000", 
              scribbleEffect: true, 
              scribbleColor: "#000000",
              scribbleIntensity: 6
            })}
            className="bg-lime-400 text-black text-sm font-bold py-2 px-3 rounded-lg hover:bg-lime-300 transition-colors"
            data-testid="preset-brat-classic"
          >
            {t.controls.presets.bratClassic}
          </Button>
          <Button
            onClick={() => onSettingsChange({ 
              backgroundColor: "#FF1493", 
              textColor: "#FFFFFF", 
              scribbleEffect: false,
              blurIntensity: 0
            })}
            className="bg-pink-500 text-white text-sm font-bold py-2 px-3 rounded-lg hover:bg-pink-400 transition-colors"
            data-testid="preset-hot-pink"
          >
            {t.controls.presets.hotPink}
          </Button>
          <Button
            onClick={() => onSettingsChange({ 
              backgroundColor: "#000000", 
              textColor: "#00FFFF", 
              scribbleEffect: false,
              blurIntensity: 2
            })}
            className="bg-cyan-400 text-black text-sm font-bold py-2 px-3 rounded-lg hover:bg-cyan-300 transition-colors"
            data-testid="preset-neon-glow"
          >
            {t.controls.presets.neonGlow}
          </Button>
          <Button
            onClick={() => onSettingsChange({ 
              backgroundColor: "#8A2BE2", 
              textColor: "#FFFFFF", 
              scribbleEffect: false,
              blurIntensity: 0
            })}
            className="bg-purple-600 text-white text-sm font-bold py-2 px-3 rounded-lg hover:bg-purple-500 transition-colors"
            data-testid="preset-royal-purple"
          >
            {t.controls.presets.royalPurple}
          </Button>
        </div>
      </div>

      {/* Colors */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <h3 className="text-brat-cyan font-bold text-lg mb-4">{t.controls.colors.title}</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <Label className="block text-white font-semibold mb-2">{t.controls.colors.background}</Label>
            <div className="flex gap-2">
              <input 
                type="color" 
                value={settings.backgroundColor}
                onChange={(e) => handleColorChange('backgroundColor', e.target.value)}
                className="w-12 h-12 rounded-lg border-2 border-brat-lime cursor-pointer"
                data-testid="input-background-color"
              />
              <Input 
                type="text" 
                value={settings.backgroundColor}
                onChange={(e) => handleHexChange('backgroundColor', e.target.value)}
                className="flex-1 bg-black border border-gray-600 rounded-lg px-3 py-2 text-white font-mono text-sm"
                data-testid="input-background-hex"
              />
            </div>
          </div>
          <div>
            <Label className="block text-white font-semibold mb-2">{t.controls.colors.textColor}</Label>
            <div className="flex gap-2">
              <input 
                type="color" 
                value={settings.textColor}
                onChange={(e) => handleColorChange('textColor', e.target.value)}
                className="w-12 h-12 rounded-lg border-2 border-brat-lime cursor-pointer"
                data-testid="input-text-color"
              />
              <Input 
                type="text" 
                value={settings.textColor}
                onChange={(e) => handleHexChange('textColor', e.target.value)}
                className="flex-1 bg-black border border-gray-600 rounded-lg px-3 py-2 text-white font-mono text-sm"
                data-testid="input-text-hex"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Font Size */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <Label className="block text-brat-lime font-bold text-lg mb-3">📏 {t.controls.textSettings.fontSize.toUpperCase()}</Label>
        <div className="flex items-center gap-4">
          <span className="text-white font-semibold">12px</span>
          <Slider
            value={[settings.fontSize]}
            onValueChange={([value]) => onSettingsChange({ fontSize: value })}
            min={12}
            max={120}
            step={1}
            className="flex-1 custom-slider"
            data-testid="slider-font-size"
          />
          <span className="text-white font-semibold">120px</span>
        </div>
        <div className="text-center mt-2">
          <span className="text-brat-cyan font-bold text-xl" data-testid="text-font-size-display">
            {settings.fontSize}px
          </span>
        </div>
      </div>

      {/* Text Alignment */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <Label className="block text-brat-lime font-bold text-lg mb-3">📐 {t.controls.textSettings.alignment.toUpperCase()}</Label>
        <div className="grid grid-cols-4 gap-2">
          {[
            { align: 'left' as const, label: 'L' },
            { align: 'center' as const, label: 'C' },
            { align: 'right' as const, label: 'R' },
            { align: 'justify' as const, label: 'J' }
          ].map(({ align, label }) => (
            <Button
              key={align}
              onClick={() => handleAlignmentChange(align)}
              className={`py-3 px-4 rounded-lg font-semibold transition-colors ${
                settings.alignment === align 
                  ? 'bg-black border-2 border-brat-lime text-white' 
                  : 'bg-black border-2 border-gray-600 text-white hover:bg-brat-lime hover:text-black'
              }`}
              data-testid={`button-align-${align}`}
            >
              {label}
            </Button>
          ))}
        </div>
      </div>

      {/* Blur Effect */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <Label className="block text-brat-lime font-bold text-lg mb-3">✨ {t.controls.effects.blur.toUpperCase()}</Label>
        <div className="flex items-center gap-4 mb-3">
          <span className="text-white font-semibold">{t.controls.effects.off}</span>
          <Slider
            value={[settings.blurIntensity]}
            onValueChange={([value]) => onSettingsChange({ blurIntensity: value })}
            min={0}
            max={10}
            step={1}
            className="flex-1 custom-slider"
            data-testid="slider-blur"
          />
          <span className="text-white font-semibold">MAX</span>
        </div>
        <div className="text-center">
          <span className="text-brat-cyan font-bold" data-testid="text-blur-display">
            {settings.blurIntensity === 0 ? 'No Blur' : `${settings.blurIntensity}px Blur`}
          </span>
        </div>
      </div>

      {/* Scribble Effect */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <Label className="text-brat-lime font-bold text-lg">🖊️ {t.controls.effects.scribble.toUpperCase()}</Label>
          <Switch
            checked={settings.scribbleEffect}
            onCheckedChange={(checked) => onSettingsChange({ scribbleEffect: checked })}
            className="data-[state=checked]:bg-brat-lime data-[state=unchecked]:bg-gray-600 border-2 data-[state=checked]:border-brat-pink data-[state=unchecked]:border-gray-500 shadow-lg data-[state=checked]:shadow-brat-lime/50"
            data-testid="switch-scribble"
          />
        </div>
        
        {settings.scribbleEffect && (
          <>
            <div className="mb-4">
              <Label className="block text-white font-semibold mb-2">{t.controls.effects.intensity}</Label>
              <div className="flex items-center gap-4 mb-3">
                <span className="text-white font-semibold">{t.controls.effects.light}</span>
                <Slider
                  value={[settings.scribbleIntensity]}
                  onValueChange={([value]) => onSettingsChange({ scribbleIntensity: value })}
                  min={1}
                  max={10}
                  step={1}
                  className="flex-1 custom-slider"
                  data-testid="slider-scribble-intensity"
                />
                <span className="text-white font-semibold">{t.controls.effects.heavy}</span>
              </div>
              <div className="text-center">
                <span className="text-brat-cyan font-bold" data-testid="text-scribble-intensity">
                  {t.controls.effects.intensity}: {settings.scribbleIntensity}
                </span>
              </div>
            </div>

            <div>
              <Label className="block text-white font-semibold mb-2">{t.controls.colors.scribbleColor}</Label>
              <div className="flex gap-2">
                <input 
                  type="color" 
                  value={settings.scribbleColor}
                  onChange={(e) => onSettingsChange({ scribbleColor: e.target.value })}
                  className="w-12 h-12 rounded-lg border-2 border-brat-lime cursor-pointer"
                  data-testid="input-scribble-color"
                />
                <Input 
                  type="text" 
                  value={settings.scribbleColor}
                  onChange={(e) => {
                    if (/^#[0-9A-F]{6}$/i.test(e.target.value)) {
                      onSettingsChange({ scribbleColor: e.target.value });
                    }
                  }}
                  className="flex-1 bg-black border border-gray-600 rounded-lg px-3 py-2 text-white font-mono text-sm"
                  data-testid="input-scribble-hex"
                />
              </div>
            </div>
          </>
        )}
      </div>

      {/* Text Transform */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <Label className="block text-brat-violet font-bold text-lg mb-4">🔄 {t.controls.transforms.title}</Label>
        
        {/* Flip Controls */}
        <div className="mb-6">
          <Label className="block text-white font-semibold mb-3">{t.controls.transforms.flipMirror}</Label>
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center justify-between bg-black rounded-lg p-3 border border-gray-600">
              <Label className="text-white font-semibold">{t.controls.transforms.flipHorizontal}</Label>
              <Switch
                checked={settings.flipHorizontal}
                onCheckedChange={(checked) => onSettingsChange({ flipHorizontal: checked })}
                className="data-[state=checked]:bg-brat-lime data-[state=unchecked]:bg-gray-600"
                data-testid="switch-flip-horizontal"
              />
            </div>
            <div className="flex items-center justify-between bg-black rounded-lg p-3 border border-gray-600">
              <Label className="text-white font-semibold">{t.controls.transforms.flipVertical}</Label>
              <Switch
                checked={settings.flipVertical}
                onCheckedChange={(checked) => onSettingsChange({ flipVertical: checked })}
                className="data-[state=checked]:bg-brat-pink data-[state=unchecked]:bg-gray-600"
                data-testid="switch-flip-vertical"
              />
            </div>
          </div>
        </div>

        {/* Rotation Control */}
        <div className="mb-4">
          <Label className="block text-white font-semibold mb-2">{t.controls.transforms.rotation}</Label>
          <div className="flex items-center gap-4 mb-3">
            <span className="text-white font-semibold">0°</span>
            <Slider
              value={[settings.rotation]}
              onValueChange={([value]) => onSettingsChange({ rotation: value })}
              min={0}
              max={360}
              step={15}
              className="flex-1 custom-slider"
              data-testid="slider-rotation"
            />
            <span className="text-white font-semibold">360°</span>
          </div>
          <div className="text-center">
            <span className="text-brat-cyan font-bold" data-testid="text-rotation-display">
              {t.controls.transforms.rotationLabel.replace('0°', `${settings.rotation}°`)}
            </span>
          </div>
        </div>

        {/* Quick Transform Presets */}
        <div>
          <Label className="block text-white font-semibold mb-3">{t.controls.transforms.quickTransform}</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={() => onSettingsChange({ 
                flipHorizontal: true, 
                flipVertical: false, 
                rotation: 0 
              })}
              className="bg-brat-lime text-black text-xs font-bold py-2 px-2 rounded-lg hover:bg-lime-300 transition-colors"
              data-testid="preset-mirror"
            >
              {t.controls.transforms.presets.mirror}
            </Button>
            <Button
              onClick={() => onSettingsChange({ 
                flipHorizontal: false, 
                flipVertical: true, 
                rotation: 0 
              })}
              className="bg-brat-pink text-white text-xs font-bold py-2 px-2 rounded-lg hover:bg-pink-400 transition-colors"
              data-testid="preset-flip"
            >
              {t.controls.transforms.presets.flip}
            </Button>
            <Button
              onClick={() => onSettingsChange({ 
                flipHorizontal: false, 
                flipVertical: false, 
                rotation: 180 
              })}
              className="bg-brat-cyan text-black text-xs font-bold py-2 px-2 rounded-lg hover:bg-cyan-300 transition-colors"
              data-testid="preset-upside-down"
            >
              {t.controls.transforms.presets.upsideDown}
            </Button>
            <Button
              onClick={() => onSettingsChange({ 
                flipHorizontal: false, 
                flipVertical: false, 
                rotation: 0 
              })}
              className="bg-gray-600 text-white text-xs font-bold py-2 px-2 rounded-lg hover:bg-gray-500 transition-colors"
              data-testid="preset-reset-transform"
            >
              {t.controls.transforms.presets.reset}
            </Button>
          </div>
        </div>
      </div>

      {/* Text Spacing */}
      <div className="bg-brat-gray rounded-2xl p-6">
        <Label className="block text-brat-lime font-bold text-lg mb-4">{t.controls.spacing.title}</Label>
        
        {/* Letter Spacing */}
        <div className="mb-6">
          <Label className="block text-white font-semibold mb-2">{t.controls.spacing.letterSpacing}</Label>
          <div className="flex items-center gap-4 mb-3">
            <span className="text-white font-semibold">{t.controls.spacing.tight}</span>
            <Slider
              value={[settings.letterSpacing]}
              onValueChange={([value]) => onSettingsChange({ letterSpacing: value })}
              min={-5}
              max={20}
              step={0.5}
              className="flex-1 custom-slider"
              data-testid="slider-letter-spacing"
            />
            <span className="text-white font-semibold">{t.controls.spacing.wide}</span>
          </div>
          <div className="text-center">
            <span className="text-brat-cyan font-bold" data-testid="text-letter-spacing">
              {t.controls.spacing.letterSpacing}: {settings.letterSpacing}px
            </span>
          </div>
        </div>

        {/* Word Spacing */}
        <div className="mb-6">
          <Label className="block text-white font-semibold mb-2">{t.controls.spacing.wordSpacing}</Label>
          <div className="flex items-center gap-4 mb-3">
            <span className="text-white font-semibold">{t.controls.spacing.close}</span>
            <Slider
              value={[settings.wordSpacing]}
              onValueChange={([value]) => onSettingsChange({ wordSpacing: value })}
              min={-10}
              max={50}
              step={1}
              className="flex-1 custom-slider"
              data-testid="slider-word-spacing"
            />
            <span className="text-white font-semibold">{t.controls.spacing.far}</span>
          </div>
          <div className="text-center">
            <span className="text-brat-pink font-bold" data-testid="text-word-spacing">
              {t.controls.spacing.wordSpacing}: {settings.wordSpacing}px
            </span>
          </div>
        </div>

        {/* Spacing Presets */}
        <div>
          <Label className="block text-white font-semibold mb-3">{t.controls.spacing.presets.title}</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={() => onSettingsChange({ 
                letterSpacing: 8, 
                wordSpacing: 25 
              })}
              className="bg-brat-lime text-black text-xs font-bold py-2 px-2 rounded-lg hover:bg-lime-300 transition-colors"
              data-testid="preset-wide-spacing"
            >
              {t.controls.spacing.presets.wideOpen}
            </Button>
            <Button
              onClick={() => onSettingsChange({ 
                letterSpacing: 15, 
                wordSpacing: 40 
              })}
              className="bg-brat-pink text-white text-xs font-bold py-2 px-2 rounded-lg hover:bg-pink-400 transition-colors"
              data-testid="preset-extra-wide"
            >
              {t.controls.spacing.presets.extraWide}
            </Button>
            <Button
              onClick={() => onSettingsChange({ 
                letterSpacing: -2, 
                wordSpacing: -5 
              })}
              className="bg-brat-cyan text-black text-xs font-bold py-2 px-2 rounded-lg hover:bg-cyan-300 transition-colors"
              data-testid="preset-tight"
            >
              {t.controls.spacing.presets.tight}
            </Button>
            <Button
              onClick={() => onSettingsChange({ 
                letterSpacing: 0, 
                wordSpacing: 0 
              })}
              className="bg-gray-600 text-white text-xs font-bold py-2 px-2 rounded-lg hover:bg-gray-500 transition-colors"
              data-testid="preset-normal-spacing"
            >
              {t.controls.spacing.presets.normal}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
