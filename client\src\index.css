@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(210 25% 7.8431%);
  --card: hsl(180 6.6667% 97.0588%);
  --card-foreground: hsl(210 25% 7.8431%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(210 25% 7.8431%);
  --primary: hsl(203.8863 88.2845% 53.1373%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(210 25% 7.8431%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(240 1.9608% 90%);
  --muted-foreground: hsl(210 25% 7.8431%);
  --accent: hsl(211.5789 51.3514% 92.7451%);
  --accent-foreground: hsl(203.8863 88.2845% 53.1373%);
  --destructive: hsl(356.3033 90.5579% 54.3137%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(201.4286 30.4348% 90.9804%);
  --input: hsl(200 23.0769% 97.4510%);
  --ring: hsl(202.8169 89.1213% 53.1373%);
  --chart-1: hsl(203.8863 88.2845% 53.1373%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(180 6.6667% 97.0588%);
  --sidebar-foreground: hsl(210 25% 7.8431%);
  --sidebar-primary: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(211.5789 51.3514% 92.7451%);
  --sidebar-accent-foreground: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-border: hsl(205.0000 25.0000% 90.5882%);
  --sidebar-ring: hsl(202.8169 89.1213% 53.1373%);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
  
  /* Brat Theme Colors */
  --brat-lime: hsl(81 100% 50%);
  --brat-pink: hsl(328 100% 54%);
  --brat-cyan: hsl(180 100% 50%);
  --brat-orange: hsl(14 100% 60%);
  --brat-violet: hsl(270 76% 53%);
  --brat-dark: hsl(0 0% 4%);
  --brat-gray: hsl(0 0% 10%);
}

.dark {
  --background: hsl(0 0% 0%);
  --foreground: hsl(200 6.6667% 91.1765%);
  --card: hsl(228 9.8039% 10%);
  --card-foreground: hsl(0 0% 85.0980%);
  --popover: hsl(0 0% 0%);
  --popover-foreground: hsl(200 6.6667% 91.1765%);
  --primary: hsl(203.7736 87.6033% 52.5490%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(195.0000 15.3846% 94.9020%);
  --secondary-foreground: hsl(210 25% 7.8431%);
  --muted: hsl(0 0% 9.4118%);
  --muted-foreground: hsl(210 3.3898% 46.2745%);
  --accent: hsl(205.7143 70% 7.8431%);
  --accent-foreground: hsl(203.7736 87.6033% 52.5490%);
  --destructive: hsl(356.3033 90.5579% 54.3137%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(210 5.2632% 14.9020%);
  --input: hsl(207.6923 27.6596% 18.4314%);
  --ring: hsl(202.8169 89.1213% 53.1373%);
  --chart-1: hsl(203.8863 88.2845% 53.1373%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(228 9.8039% 10%);
  --sidebar-foreground: hsl(0 0% 85.0980%);
  --sidebar-primary: hsl(202.8169 89.1213% 53.1373%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(205.7143 70% 7.8431%);
  --sidebar-accent-foreground: hsl(203.7736 87.6033% 52.5490%);
  --sidebar-border: hsl(205.7143 15.7895% 26.0784%);
  --sidebar-ring: hsl(202.8169 89.1213% 53.1373%);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00), 0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0.00);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

.gradient-bg {
  background: linear-gradient(135deg, 
    #0a0a0a 0%, 
    #1a1a1a 25%, 
    #141414 50%, 
    #0f0f0f 75%, 
    #050505 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
}

.hero-gradient {
  background: linear-gradient(135deg, 
    rgba(129, 255, 0, 0.1) 0%,
    rgba(255, 0, 128, 0.1) 25%,
    rgba(0, 255, 255, 0.1) 50%,
    rgba(129, 255, 0, 0.05) 75%,
    transparent 100%);
}

.glass-card {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(129, 255, 0, 0.2);
}

.floating-animation {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from { box-shadow: 0 0 20px rgba(129, 255, 0, 0.3); }
  to { box-shadow: 0 0 40px rgba(129, 255, 0, 0.6), 0 0 60px rgba(129, 255, 0, 0.3); }
}

.nav-link {
  @apply px-4 py-2 rounded-lg text-white font-semibold transition-all duration-300 hover:bg-white/10;
}

.footer-link {
  @apply block text-gray-400 hover:text-brat-lime transition-colors duration-300 font-medium;
}

.neon-border {
  box-shadow: 0 0 20px hsl(var(--brat-lime) / 0.3), inset 0 0 20px hsl(var(--brat-lime) / 0.1);
}

.glow-text {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 40px currentColor;
}

.custom-slider {
  appearance: none;
  background: linear-gradient(90deg, var(--brat-lime), var(--brat-pink), var(--brat-cyan));
  height: 8px;
  border-radius: 4px;
  outline: none;
}

.custom-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: #FFFFFF;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid var(--brat-lime);
}

.custom-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #FFFFFF;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid var(--brat-lime);
}

.font-brat {
  font-family: 'Inter', Arial, sans-serif;
}
